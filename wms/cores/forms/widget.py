from django.forms import widgets
from django_filters.widgets import RangeWidget


class CoreBaseWidget:
    """
    Base mixin for all core widgets providing common functionality.
    Supports dynamic sizing through FormFieldSize constants.

    Features:
    - Default styling with Tailwind classes
    - Dynamic size support
    - Attribute merging
    - Theme integration
    """
    default_attrs = {
        'class': 'appearance-none w-[75%] block px-3 py-1 text-base text-theme-text-primary rounded-xs border bg-theme-bg-primary border-theme-input-border-primary disabled:bg-theme-input-disabled focus:border-theme-primary focus:outline-none'
    }

    def __init__(self, attrs=None, *args, **kwargs):
        final_attrs = self.default_attrs.copy()

        # Extract size from kwargs if provided
        size = kwargs.pop('size', '')
        if size:
            classes = final_attrs.get('class', '').split()
            # Remove any existing width classes
            classes = [c for c in classes if not c.startswith('w-')]
            # Add new size class
            classes.append(size)
            final_attrs['class'] = ' '.join(classes)

        if attrs:
            if 'class' in attrs and 'class' in final_attrs:
                existing_classes = final_attrs['class'].split()
                new_classes = attrs.pop('class').split()

                # Extract width classes from new_classes
                width_classes = [c for c in new_classes if c.startswith('w-')]

                # Remove any existing width classes from existing_classes
                existing_classes = [c for c in existing_classes if not c.startswith('w-')]

                # Merge non-width classes, removing duplicates while preserving order
                final_classes = []
                for cls in existing_classes + [c for c in new_classes if not c.startswith('w-')]:
                    if cls not in final_classes:
                        final_classes.append(cls)

                # Add width classes at the end (they will override any previous width settings)
                final_classes.extend(width_classes)

                final_attrs['class'] = ' '.join(final_classes)
            final_attrs.update(attrs)
        super().__init__(attrs=final_attrs, *args, **kwargs)


class CoreDateWidget(CoreBaseWidget, widgets.DateInput):
    template_name = 'cores/widgets/date_picker.html'
    default_attrs = {
        'class': 'appearance-none w-full block px-3 py-1 text-base text-theme-text-primary rounded-xs border bg-theme-bg-primary border-theme-input-border-primary focus:border-theme-primary focus:outline-none',
        'data-calendar-date': 'true',
        'type': 'text',
        'autocomplete': 'off'
    }

    def __init__(self, attrs=None):
        super().__init__(attrs=attrs, format='%Y-%m-%d')


class CoreDateTimeWidget(CoreBaseWidget, widgets.DateTimeInput):
    template_name = 'cores/widgets/datetime_picker.html'
    default_attrs = {
        'class': 'appearance-none w-full block pl-3 px-3 py-1 text-base text-theme-text-primary rounded-xs border bg-theme-bg-primary border-theme-input-border-primary focus:border-theme-primary focus:outline-none',
        'data-calendar-datetime': 'true',
        'type': 'text',
        'autocomplete': 'off'
    }

    def __init__(self, attrs=None):
        super().__init__(attrs=attrs, format='%Y-%m-%d %I:%M %p')


class CoreTextWidget(CoreBaseWidget, widgets.TextInput):
    default_attrs = {
        **CoreBaseWidget.default_attrs,
        'autocomplete': 'off'
    }
    template_name = "tailwind/text.html"


class CoreTextAreaWidget(CoreBaseWidget, widgets.Textarea):
    default_attrs = {
        **CoreBaseWidget.default_attrs,
        'class': f'{CoreBaseWidget.default_attrs['class']}',
        'cols': 80,
        'rows': 5,
    }


class CoreNumberWidget(CoreBaseWidget, widgets.NumberInput):
    pass


class CoreEmailWidget(CoreBaseWidget, widgets.EmailInput):
    pass


class CoreURLWidget(CoreBaseWidget, widgets.URLInput):
    pass


class CorePasswordWidget(CoreBaseWidget, widgets.PasswordInput):
    pass


class CoreHiddenWidget(CoreBaseWidget, widgets.HiddenInput):
    pass


class CoreMultipleHiddenWidget(CoreBaseWidget, widgets.MultipleHiddenInput):
    pass


class CoreFileWidget(CoreBaseWidget, widgets.FileInput):
    default_attrs = {
        'class': 'appearance-none block w-full px-3 py-2 rounded-sm border border-theme-input-border-primary focus:border-theme-input-border-focus file:mr-4 file:py-2 file:px-4 file:rounded-sm file:border-0 file:bg-theme-primary hover:file:bg-theme-primary-hover'
    }


class CoreClearableFileWidget(CoreBaseWidget, widgets.ClearableFileInput):
    default_attrs = {
        'class': 'appearance-none block w-full px-3 py-2 rounded-sm border border-theme-input-border-primary focus:border-theme-input-border-focus file:mr-4 file:py-2 file:px-4 file:rounded-sm file:border-0 file:bg-theme-primary hover:file:bg-theme-primary-hover'
    }


class CoreTimeWidget(CoreBaseWidget, widgets.TimeInput):
    template_name = 'cores/widgets/timepicker.html'
    default_attrs = {
        **CoreBaseWidget.default_attrs,
        'type': 'time'
    }


class CoreCheckboxWidget(CoreBaseWidget, widgets.CheckboxInput):
    template_name = 'cores/widgets/checkbox.html'
    default_attrs = {
        'class': 'h-4 w-4 rounded border-theme-input-border-primary text-theme-primary focus:ring-theme-primary'
    }

    def __init__(self, attrs=None, label=None):
        if attrs is None:
            attrs = {}

        # Add label if provided
        if label:
            attrs['label'] = label

        super().__init__(attrs=attrs)


class CoreSelectWidget(CoreBaseWidget, widgets.Select):
    default_attrs = {
        'class': 'appearance-none block px-3 py-1 text-base text-theme-text-primary rounded-xs border bg-theme-bg-primary border-theme-input-border-primary focus:border-theme-primary focus:outline-none django-select2'
    }


class CoreNullBooleanSelectWidget(CoreSelectWidget, widgets.NullBooleanSelect):
    pass


class CoreSelectMultipleWidget(CoreBaseWidget, widgets.SelectMultiple):
    """
    Enhanced select multiple widget using select2.js
    Features:
    - Searchable dropdown
    - Multiple selection support
    - Theme-consistent styling
    """
    default_attrs = {
        'class': 'appearance-none block px-3 py-2 rounded-sm border border-theme-input-border-primary focus:border-theme-input-border-focus django-select2'
    }


class CoreRadioSelectWidget(CoreBaseWidget, widgets.RadioSelect):
    option_template_name = 'cores/widgets/radio_option.html'
    default_attrs = {
        'class': 'h-4 w-full border-theme-input-border-primary text-theme-primary focus:ring-theme-primary'
    }


class CoreCheckboxSelectMultipleWidget(CoreBaseWidget, widgets.CheckboxSelectMultiple):
    template_name = 'cores/widgets/tree_checkbox_option.html'
    default_attrs = {
        'class': 'h-4 w-4 rounded focus:ring-theme-primary'
    }

    def create_option(self, name, value, label, selected, index, subindex=None, attrs=None):
        option = super().create_option(name, value, label, selected, index, subindex, attrs)

        if hasattr(value, 'instance'):
            instance = value.instance
            option['attrs'].update({
                'level': instance.depth,
                'is_parent': instance.get_children().exists(),
                'is_leaf': instance.is_leaf(),
                'parent_id': instance.get_parent().pk if instance.get_parent() else None,
            })

        return option

class CoreSplitDateTimeWidget(CoreBaseWidget, widgets.SplitDateTimeWidget):
    pass


class CoreSplitHiddenDateTimeWidget(CoreBaseWidget, widgets.SplitHiddenDateTimeWidget):
    pass


class CoreSelectDateWidget(CoreBaseWidget, widgets.SelectDateWidget):
    pass


class CoreBooleanWidget(CoreBaseWidget, widgets.CheckboxInput):
    template_name = 'cores/widgets/boolean.html'
    default_attrs = {}

    def __init__(self, attrs=None, label=None, description=None):
        if attrs is None:
            attrs = {}

        # Add label if provided
        if label:
            attrs['label'] = label

        # Add description if provided
        if description:
            attrs['description'] = description

            # Set up accessibility attributes
            if 'id' in attrs:
                attrs['aria_describedby'] = f"{attrs['id']}-description"

        super().__init__(attrs=attrs)


class CoreDateRangeWidget(CoreBaseWidget, RangeWidget):
    """
    A widget for a date range filter using CoreDateWidget.
    Uses 'after' and 'before' suffixes for parameter names.
    """
    template_name = 'cores/widgets/date_range_picker.html'
    suffixes = ['after', 'before']

    def __init__(self, attrs=None):
        if attrs is None:
            attrs = {}
        # Create widgets with the proper attributes
        widgets = (
            CoreDateWidget(attrs=attrs),
            CoreDateWidget(attrs=attrs)
        )
        super(RangeWidget, self).__init__(widgets, attrs)


class CoreDateTimeRangeWidget(CoreBaseWidget, RangeWidget):
    """
    A widget for a date range filter using CoreDateWidget.
    Uses 'after' and 'before' suffixes for parameter names.
    """
    template_name = 'cores/widgets/datetime_range_picker.html'
    suffixes = ['after', 'before']

    def __init__(self, attrs=None):
        if attrs is None:
            attrs = {}
        # Create widgets with the proper attributes
        widgets = (
            CoreDateTimeWidget(attrs=attrs),
            CoreDateTimeWidget(attrs=attrs)
        )
        super(RangeWidget, self).__init__(widgets, attrs)
