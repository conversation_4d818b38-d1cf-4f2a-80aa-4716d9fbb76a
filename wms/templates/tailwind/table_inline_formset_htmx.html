{% load crispy_forms_tags %}
{% load crispy_forms_utils %}
{% load tailwind_field %}

{% specialspaceless %}
{% if formset_tag %}
  <form {{ flat_attrs|safe }}
        method="{{ form_method }}"
        {% if formset.is_multipart %}enctype="multipart/form-data"{% endif %}>
  {% endif %}
  {% if formset_method|lower == 'post' and not disable_csrf %}
    {% csrf_token %}
  {% endif %}
  <div>{{ formset.management_form|crispy }}</div>
  <div class="grid grid-cols-[minmax(9rem,auto)_1fr] gap-0">
    <label class="flex items-baseline p-3 pt-2 justify-end text-end text-theme-text-primary text-sm min-w-38 max-w-38 font-medium bg-theme-form-label">
      {{ formset_header_title|safe }}
    </label>
    <div class="flex flex-col overflow-x-auto overflow-y-hidden">
      <table id="{{ formset.prefix }}-formset-table"
             class="{{ formset.prefix }}-formset-container overflow-y-hidden">
        <thead>
          {% if formset.readonly and not formset.queryset.exists %}
          {% else %}
            <tr>
              {% for field in formset.forms.0 %}
                {% if field.label and not field.is_hidden and field.name != 'DELETE' %}
                  <th for="{{ field.auto_id }}"
                      class="border-x-1 border-gray-200 px-2 py-2 text-left text-theme-text-primary font-medium text-sm bg-theme-form-label">
                    {% if field.field.required and not field|is_checkbox %}<span class="text-red-500">*</span>{% endif %}
                    {{ field.label|safe }}
                  </th>
                {% endif %}
              {% endfor %}
              <th scope="col"
                  class="border-x-1 border-gray-200 px-2 py-2 text-left text-theme-text-primary font-medium text-sm bg-theme-form-label">
                <div class="flex justify-end">
                  <button type="button"
                          class="add-row inline-flex items-center bg-theme-action-create px-2 py-1 border border-transparent text-sm leading-4 font-medium rounded-xs text-white">
                    ADD
                  </button>
                </div>
              </th>
            </tr>
          {% endif %}
        </thead>
        <tbody>
          {% for form in formset %}
            {% if form_show_errors and not form.is_extra %}
              {% include "tailwind/errors_formset.html" %}
            {% endif %}
            <tr>
              {% for field in form %}
                {% if field.name != 'DELETE' %}
                  {% include "tailwind/inline_field.html" with tag="td" form_show_labels=False field_class="" %}
                {% endif %}
              {% endfor %}
              {# --- ADDED: Actions Cell --- #}
              <td class="px-2 py-0 whitespace-nowrap text-center">
                {% if form.DELETE %}
                  <button type="button" class="delete-row text-red-600 hover:text-red-900">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                {% endif %}
              </td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
      {#    <div class="mt-4">{% include "tailwind/inputs.html" %}</div>#}
    </div>
  </div>
  {% if formset_tag %}</form>{% endif %}
{% endspecialspaceless %}
<script>
  // Initialize FormsetRowHandler immediately for HTMX-loaded content
  // This runs immediately when the template is rendered, not waiting for DOMContentLoaded
  (function() {
    console.log("Initializing FormsetRowHandler for prefix: {{ formset.prefix }}");
    
    // Small delay to ensure DOM elements are fully rendered
    setTimeout(function() {
      if (typeof FormsetRowHandler !== 'undefined') {
        new FormsetRowHandler("{{ formset.prefix }}");
        
        // Initialize Select2 on existing elements if available
        const formsetTable = document.getElementById('{{ formset.prefix }}-formset-table');
        if (formsetTable) {
          const select2Elements = formsetTable.querySelectorAll('.django-select2');
          if (select2Elements.length > 0 && typeof initializeSelect2 !== 'undefined') {
            initializeSelect2($(select2Elements));
          }
        }
      } else {
        console.warn('FormsetRowHandler not available');
      }
    }, 100);
  })();
</script>
