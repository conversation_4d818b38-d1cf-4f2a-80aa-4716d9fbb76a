import json

from django.contrib import messages
from django.db.models import QuerySet
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, render, redirect
from django.urls import reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_POST

from wms.cores.mixins import ExportTableMixin
from wms.cores.views import CoreSingleTableView, CoreDetailView, CoreDataTableDetailView

from wms.apps.pickings.models import PickingList, PickingListItem
from wms.apps.pickings.tables.picking_list import PickingListTable, PickingListDetailTable
from wms.apps.pickings.tables.picking_list_item import PickingListItemTable
from wms.apps.pickings.tables.picking_list_release_order import PickingListReleaseOrderTable
from wms.apps.pickings.filters import PickingListFilter
from wms.apps.releases.models import WarehouseReleaseOrder


class PickingListListView(ExportTableMixin, CoreSingleTableView):
    """
    View for displaying and managing the list of Picking Lists.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    - Filtering
    """
    model = PickingList
    table_class = PickingListTable
    template_name = "cores/list_table.html"
    partial_template_name = "cores/table_partial.html"
    filterset_class = PickingListFilter

    # Search configuration
    search_fields = [
        "system_number",
        "name",
        "status",
        "issued_by__username"
    ]

    # Export configuration
    export_name = "picking_lists"
    export_permission = []  # Empty list means no specific permissions required

    # Permission configuration
    permission_required = ("pickings.view_pickinglist",)

    def get_queryset(self) -> QuerySet:
        """
        Return all picking lists filtered by user's warehouses.
        """
        queryset = super().get_queryset()
        user_warehouse = list(self.request.user.warehouses.all().values_list("pk", flat=True))
        return queryset.filter(release_from__pk__in=user_warehouse).distinct()


picking_list_list_view = PickingListListView.as_view()


class PickingListDetailHomeView(CoreDetailView):
    """View for displaying the details of a Picking List."""
    model = PickingList
    template_name = "pickings/picking_lists/home.html"
    context_object_name = "picking_list"


class PickingListDetailView(CoreDetailView):
    """View for displaying the details of a Picking List."""
    model = PickingList
    template_name = "pickings/picking_lists/partials/detail.html"
    context_object_name = "picking_list"


class PickingListDataTableDetailView(CoreDataTableDetailView):
    """View that combines a detail view with a data table for Picking Lists."""
    model = PickingList
    table_class = PickingListDetailTable
    context_object_name = "picking_list"
    partial_view = PickingListDetailHomeView
    search_fields = ["system_number", "status"]

class PickingListItemListView(ExportTableMixin, CoreSingleTableView):
    """View for displaying and managing the list of Picking List Items."""
    model = PickingListItem
    table_class = PickingListItemTable
    template_name = "pickings/picking_lists/picklist_detail_item_view_table.html"
    partial_template_name = "cores/table_partial.html"

    # Search configuration
    search_fields = [
        "picking_list__system_number",
        "stock__item__code",
        "stock__item__name",
        "stock__batch_no",
    ]

    # Export configuration
    export_name = "picking_list_items"
    export_permission = []  # Empty list means no specific permissions required

    htmx_target = 'table-content-partial-release-orders-items'

    def get_queryset(self):
        """Filter items by the picking list if specified."""
        queryset = super().get_queryset()
        return queryset.filter(picking_list_id=self.kwargs['pk'])

    def get_htmx_base_url(self):
        """
        Override to provide the dynamic URL based on the current item's PK.
        This URL will be used for all HTMX requests and querystring operations.
        """
        pk = self.kwargs.get('pk')
        if pk:
            return reverse("pickings:picking_lists:item_list", kwargs={"pk": pk})
        return None

class PickingListReleaseOrderListView(ExportTableMixin, CoreSingleTableView):
    """View for displaying and managing the list of Release Orders associated with a Picking List."""
    model = WarehouseReleaseOrder
    table_class = PickingListReleaseOrderTable
    template_name = "pickings/picking_lists/picklist_detail_release_order_view_table.html"
    partial_template_name = "cores/table_partial.html"

    # Search configuration
    search_fields = [
        "system_number",
        "deliveryorder__system_number",
        "customer_reference",
        "consignee__display_name",
    ]

    # Export configuration
    export_name = "picking_list_release_orders"
    export_permission = []  # Empty list means no specific permissions required

    htmx_target = 'table-content-partial-release-orders'
    table_height = 'h-fit'

    def get_queryset(self):
        """Filter release orders by the picking list if specified."""
        queryset = super().get_queryset()
        return queryset.filter(picking_list_id=self.kwargs['pk'])

    def get_htmx_base_url(self):
        """
        Override to provide the dynamic URL based on the current item's PK.
        This URL will be used for all HTMX requests and querystring operations.
        """
        pk = self.kwargs.get('pk')
        if pk:
            return reverse("pickings:picking_lists:release_order_list", kwargs={"pk": pk})
        return None


def picking_list_release_order_delete_form(request, pk):
    """
    Display the confirmation form for removing a release order from a picking list.
    """
    release_order = get_object_or_404(WarehouseReleaseOrder, pk=pk)

    # Check if the release order is associated with a picking list
    if not release_order.picking_list:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "This release order is not associated with any picking list.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request, _("This release order is not associated with any picking list."))
        return HttpResponse(status=400, content="Release order not associated with a picking list.")

    # Check if the picking list is in a status that allows removing release orders
    if release_order.picking_list.status != PickingList.Status.NEW:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Release orders can only be removed from picking lists in 'New' status.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request, _("Release orders can only be removed from picking lists in 'New' status."))
        return HttpResponse(status=400, content="Picking list not in 'New' status.")

    context = {
        'release_order': release_order,
        'request': request,
    }

    return render(request, 'pickings/picking_lists/partials/release_order_delete_form.html', context)


@require_POST
def picking_list_release_order_delete(request, pk):
    """
    Remove a release order from a picking list.
    """
    release_order = get_object_or_404(WarehouseReleaseOrder, pk=pk)

    # Check if the release order is associated with a picking list
    if not release_order.picking_list:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "This release order is not associated with any picking list.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request, _("This release order is not associated with any picking list."))
        return HttpResponse(status=400, content="Release order not associated with a picking list.")

    # Check if the picking list is in a status that allows removing release orders
    picking_list = release_order.picking_list
    if picking_list.status != PickingList.Status.NEW:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Release orders can only be removed from picking lists in 'New' status.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request, _("Release orders can only be removed from picking lists in 'New' status."))
        return HttpResponse(status=400, content="Picking list not in 'New' status.")

    try:
        # Store the picking list ID before removing the association
        picking_list_id = picking_list.id

        # Remove the association between the release order and the picking list
        release_order.picking_list = None
        release_order.save(update_fields=['picking_list'])

        # Regenerate the picking list items
        picking_list.regenerate_picking_list_item()

        # For HTMX requests, return a success response with a refresh trigger
        if request.headers.get('HX-Request'):
            # Get the URL for the release order list view
            release_order_list_url = reverse('pickings:picking_lists:detail', kwargs={'pk': picking_list_id})

            # Set up HTMX triggers for success notification, modal close, and content refresh
            trigger_data = {
                "closeModalEvent": None,
                "showNotificationEvent": {
                    "message": "Release order removed from picking list successfully!",
                    "type": "success"
                },
                "refreshTabContent": {
                    "url": release_order_list_url
                }
            }

            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }

            # Return an empty response with headers
            return HttpResponse('', headers=headers, status=200)

        # For regular form submissions, return a success message
        messages.success(request, _("Release order removed from picking list successfully."))
        return HttpResponse(status=200)
    except Exception as e:
        messages.error(request, _("Failed to remove release order from picking list: %(error)s") % {'error': str(e)})
        # Return an error response for HTMX
        return HttpResponse(status=500, content=f"Error removing release order: {str(e)}")


@require_POST
def picking_list_complete(request, pk):
    """
    Mark a picking list as completed.
    """
    picking_list = get_object_or_404(PickingList, pk=pk)

    # Check if the picking list is in a status that allows marking as completed
    allowed_statuses = [
        PickingList.Status.NEW,
        PickingList.Status.PROCESSING
    ]

    if picking_list.status not in allowed_statuses:
        messages.error(request, _("Only Picking Lists in 'New' or 'Processing' status can be marked as completed."))
        return HttpResponse(status=400, content="Picking list not eligible for complete action.")

    try:
        # Update the picking list status to COMPLETED
        picking_list.status = PickingList.Status.COMPLETED
        picking_list.completion_datetime = timezone.now()
        picking_list.save(update_fields=['status', 'completion_datetime'])

        # For HTMX requests, return success response
        if request.headers.get('HX-Request'):
            # Get the URL for the release order list view
            release_order_list_url = reverse('pickings:picking_lists:detail', kwargs={'pk': pk})
            trigger_data = {
                "showNotificationEvent": {
                    "message": "Picking list marked as completed successfully!",
                    "type": "success"
                },
                "refreshTabContent": {
                    "url": release_order_list_url
                },
            }

            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            return HttpResponse(status=200, headers=headers)

        # For non-HTMX requests, redirect back to the detail page with success message
        messages.success(request, _("Picking list marked as completed successfully."))
        return HttpResponse(status=200)

    except Exception as e:
        # If an error occurs, return an error response
        if request.headers.get('HX-Request'):
            trigger_data = {
                "showNotificationEvent": {
                    "message":  _("Failed to mark picking list as completed: %(error)s") % {'error': str(e)},
                    "type": "error"
                }
            }
            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            return HttpResponse(status=500, headers=headers)

        messages.error(request, _("Failed to mark picking list as completed: %(error)s") % {'error': str(e)})
        return HttpResponse(status=500, content=f"Error completing picking list: {str(e)}")


###########################################################################
# picking list delete
###########################################################################


def picking_list_delete_form(request, pk):
    """
    Display the form for deleting a picking list.
    """
    picking_list = get_object_or_404(PickingList, pk=pk)

    # Check if the picking list is in a status that allows deletion
    allowed_statuses = [
        PickingList.Status.NEW,
    ]

    if picking_list.status not in allowed_statuses:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Only Picking List in 'New' status can be deleted.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request,
                       _("Only Picking List in 'New' status can be deleted."))
        return HttpResponse(status=400, content="Picking List not eligible for deletion.")

    context = {
        'picking_list': picking_list,
        'request': request,
    }

    return render(request, 'pickings/picking_lists/partials/picking_list_delete_form.html', context)


@require_POST
def picking_list_delete(request, pk):
    """
    Delete a picking list.
    """
    picking_list = get_object_or_404(PickingList, pk=pk)

    # Check if the picking list is in a status that allows deletion
    allowed_statuses = [
        PickingList.Status.NEW,
    ]

    if picking_list.status not in allowed_statuses:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Only Picking List in 'New' status can be deleted.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request,
                       _("Only Picking List in 'New' status can be deleted."))
        return HttpResponse(status=400, content="Picking List not eligible for deletion.")

    try:
        # Attempt to delete the picking list
        picking_list.delete()

        # For HTMX requests, return success response
        if request.headers.get('HX-Request'):
            trigger_data = {
                "closeModalEvent": None,
                "showNotificationEvent": {
                    "message": "Picking List deleted successfully!",
                    "type": "success"
                },
                # Redirect to the list page after deletion
                "redirectEvent": {"url": reverse("pickings:picking_lists:list")}
            }

            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            return HttpResponse(status=200, headers=headers)

        # For non-HTMX requests, redirect to the list page
        messages.success(request, _("Picking List deleted successfully."))
        return redirect(reverse("pickings:picking_lists:list"))

    except Exception as e:
        # Handle validation errors or other exceptions
        error_message = str(e)

        # For HTMX requests, return error response
        if request.headers.get('HX-Request'):
            trigger_data = {
                "closeModalEvent": None,
                "showNotificationEvent": {
                    "message": f"Failed to delete picking list: {error_message}",
                    "type": "error"
                }
            }

            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            return HttpResponse(status=400, headers=headers)

        # For non-HTMX requests, redirect back with error message
        messages.error(request, _("Failed to delete picking list: %(error)s") % {'error': error_message})
        return redirect(reverse("pickings:picking_lists:panel", kwargs={"pk": pk}))


###########################################################################
# end picking list delete
###########################################################################


# Register view functions
picking_list_detail_home_view = PickingListDetailHomeView.as_view()
picking_list_detail_view = PickingListDetailView.as_view()
picking_list_data_table_detail_view = PickingListDataTableDetailView.as_view()
picking_list_item_list_view = PickingListItemListView.as_view()
picking_list_release_order_list_view = PickingListReleaseOrderListView.as_view()

# Add more views as needed for the picking list functionality
