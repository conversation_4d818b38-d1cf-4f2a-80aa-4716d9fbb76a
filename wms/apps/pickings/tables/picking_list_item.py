import django_tables2 as tables
from django.conf import settings
from django.db.models import Sum
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html

from wms.cores.utils import normalize_decimal
from wms.cores.models import DISPLAY_EMPTY_VALUE


from wms.apps.pickings.models import PickingListItem
from wms.apps.rackings.models import RackTransaction
from wms.apps.releases.models import WarehouseReleaseOrderItem


class PickingListItemTable(tables.Table):
    """Table for displaying Picking List Items."""
    row_number = tables.Column(
        verbose_name="#",
        empty_values=(),
        orderable=True,
        order_by="sort_order"
    )
    stock__item__code = tables.Column(verbose_name=_("Code"))
    stock__item__name = tables.Column(verbose_name=_("Name"))
    stock__batch_no = tables.Column(verbose_name=_("Batch"))
    stock__expiry_date = tables.DateColumn(
        verbose_name=_("Expiry"),
        format=settings.DATE_FORMAT,
        attrs={
            "td": {
                "class": "whitespace-nowrap",
            },
        }
    )
    consolidated_quantity = tables.Column(verbose_name=_("Quantity"))
    stock__item__uom__symbol = tables.Column(verbose_name=_("UOM"))
    outbound_uom_display_conversion = tables.Column(verbose_name=_("CARTON"), accessor="stock")
    stock__db_balance = tables.Column(verbose_name=_("SOH"))
    rack_available_transaction = tables.Column(verbose_name=_("Avail Rack"), accessor="stock")
    reserved = tables.Column(verbose_name=_("Reserved"), accessor="stock")

    class Meta:
        model = PickingListItem
        order_by = 'sort_order'
        template_name = "tables/table_htmx.html"
        row_attrs = {
            "id": lambda record: f"item-row-{record.pk}"
        }

        fields = (
            "row_number",
            "stock__item__code",
            "stock__item__name",
            "stock__batch_no",
            "stock__expiry_date",
            "consolidated_quantity",
            "stock__item__uom__symbol",
            # "outbound_uom_display_conversion",
            "stock__db_balance",
            "reserved",
        )
        sequence = [
            "row_number",
            "stock__item__code",
            "stock__item__name",
            "stock__batch_no",
            "stock__expiry_date",
            "consolidated_quantity",
            "stock__item__uom__symbol",
            "outbound_uom_display_conversion",
            "stock__db_balance",
            "rack_available_transaction",
            "reserved",
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.counter = 0

    def render_row_number(self, value, record):
        # Use the get_position method from the model to get the fixed position
        # This ensures the row number stays consistent regardless of sorting
        return str(record.get_position)

    def render_status(self, value, record):
        """Return nice html label display for status."""
        return record.html_status_display if hasattr(record, 'html_status_display') else value

    def render_stock__db_balance(self, value, record):
        """Format the stock on hand value."""
        if value is None:
            return "-"
        return round(value, record.stock.item.uom.unit_precision)

    def render_rack_available_transaction(self, value, record):
        """Format the rack available quantity value."""
        # return round(value, record.stock.item.uom.unit_precision)
        return record.get_total_available_rack_transaction_balance

    def render_reserved(self, value, record):
        """Format the reserved amount value."""

        involved_wro_pk_list = list(record.picking_list.warehousereleaseorder_set.all().values_list("pk", flat=True))
        involved_wro_item_pk_list = list(
            WarehouseReleaseOrderItem.objects.filter(
                release_order__pk__in=involved_wro_pk_list
            ).values_list("pk", flat=True)
        )

        rackstorage = RackTransaction.objects.filter(
            rackstorage__stock=record.stock,
            warehouse_release_order_item__pk__in=involved_wro_item_pk_list,
            is_freezed=False,
        ).values('rackstorage').distinct().annotate(
            sum=Sum("quantity"),
        ).values_list("rackstorage__rack__full_name", "sum")

        return format_html(
            "<br />".join([f"{rack}[{abs(normalize_decimal(quantity))}]" for rack, quantity in rackstorage])
        ) or DISPLAY_EMPTY_VALUE

    def render_consolidated_quantity(self, value, record):
        """Format the quantity value."""
        if value is None:
            return "-"
        return round(value, record.stock.item.uom.unit_precision)

    def render_outbound_uom_display_conversion(self, value, record):
        """Format the carton value."""
        prepared_str = ""
        if record.get_outbound_uom_display_conversion:
            prepared_str = format_html(
                f"{record.get_outbound_uom_display_conversion["Carton"]["converted_quantity"]} "
                f"{record.get_outbound_uom_display_conversion["Carton"]["converted_uom"]} <br />"
                f"{record.get_outbound_uom_display_conversion["Carton"]["base_quantity"]} PCE"
            )
        return prepared_str
