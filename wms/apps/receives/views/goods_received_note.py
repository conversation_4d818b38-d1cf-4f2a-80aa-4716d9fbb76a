from crispy_forms.helper import FormHelper
import json

from django.contrib import messages
from django.db import transaction
from django.db.models import QuerySet
from django.forms import inlineformset_factory, ValidationError
from django.http import HttpResponse
from django.shortcuts import redirect, render, get_object_or_404
from django.urls import reverse_lazy, reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_POST

from wms.cores.mixins import ExportTableMixin
from wms.cores.views import CoreSingleTableView, CoreCreateView, CoreUpdateView, CoreDetailView, CoreDataTableDetailView

from wms.apps.releases.models import WarehouseReleaseOrder

from wms.apps.receives.models import GoodsReceivedNote, GoodsReceivedNoteItem, GoodsReceivedNoteStockIn
from wms.apps.receives.tables import GoodsReceivedNoteTable, GoodsReceivedNoteDetailTable, GoodsReceivedNoteItemTable
from wms.apps.receives.filters import GoodsReceivedNoteFilter
from wms.apps.receives.forms import GoodsReceivedNoteForm, GoodsReceivedNoteItemForm


class GoodsReceivedNoteListView(ExportTableMixin, CoreSingleTableView):
    """
    View for displaying and managing the list of GRN.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    - Filtering
    """
    model = GoodsReceivedNote
    table_class = GoodsReceivedNoteTable
    template_name = "cores/list_table.html"
    partial_template_name = "cores/table_partial.html"
    filterset_class = GoodsReceivedNoteFilter

    # Search configuration
    search_fields = [
        "system_number",
        "customer_reference",
        "consignor__display_name",
        "consignor_inbound_delivery_no",
        "issued_by__username"
    ]

    # Export configuration
    export_name = "goods_received_notes"
    export_permission = []  # Empty list means no specific permissions required

    # Permission configuration
    permission_required = ("receives.view_goodsreceivednote",)

    # def get_queryset(self) -> QuerySet:
    #     """
    #     Return all warehouse grns filtered by user's warehouses.
    #     """
    #     queryset = super().get_queryset()
    #     user_warehouse = list(self.request.user.warehouses.all().values_list("pk", flat=True))
    #     return queryset.filter(warehouses__pk__in=user_warehouse).distinct()

    def get_queryset(self):
        queryset = super().get_queryset()

        # Example: only show items related to the user's company
        consignor_filter = self.request.user.consignor_filter

        if consignor_filter:
            return queryset.filter(consignor=consignor_filter)
        else:
            return queryset


goods_received_note_list_view = GoodsReceivedNoteListView.as_view()


class GoodsReceivedNoteFormsetMixin:
    """Mixin to handle GoodsReceivedNoteItem inline formset with Crispy Forms (Table Layout)."""
    formset = None

    def get_initial_items(self, warehouse_release_order: WarehouseReleaseOrder = None):
        """
        To retrieve and derive a list of initial GoodsReceivedNoteItems from the WarehouseReleaseOrderItems
        of the given WarehouseReleaseOrder.
        """
        items = []

        if warehouse_release_order:
            for wro_item in warehouse_release_order.warehouse_release_order_items.all():
                items.append(
                    {
                        "item": wro_item.item,
                        "batch_no": wro_item.batch_no,
                        "expiry_date": wro_item.expiry_date,
                        "quantity": wro_item.quantity,
                        "uom": wro_item.uom,
                    }
                )

        return items

    def get_formset(self):
        """Creates and returns the GoodsReceivedNoteItem formset with a Crispy Helper for table layout."""
        goods_received_note_item_form_set = inlineformset_factory(
            GoodsReceivedNote,
            GoodsReceivedNoteItem,
            form=GoodsReceivedNoteItemForm,
            extra=1,
            validate_min=True,
            # can_delete=True
        )

        # Create the helper for the formset
        helper = FormHelper()
        helper.form_tag = False
        helper.disable_csrf = True
        helper.template = 'tailwind/table_inline_formset.html'
        helper.formset_header_title = ""

        if self.request.method == 'POST':
            formset_instance = goods_received_note_item_form_set(
                self.request.POST,
                self.request.FILES,
                instance=self.object,
                prefix='items'
            )
        else:
            initial = []

            # If warehouse_release_order is available, preload item data
            if hasattr(self, "warehouse_release_order") and self.warehouse_release_order:
                initial = self.get_initial_items(self.warehouse_release_order)
                goods_received_note_item_form_set.extra = len(initial) if initial else 1

            # For update view, set extra=0 if there are existing items
            else:
                initial_extra = 0 if (self.object and self.object.goodsreceivednoteitem_set.all().exists()) else 1
                goods_received_note_item_form_set.extra = initial_extra

            formset_instance = goods_received_note_item_form_set(
                instance=self.object,
                prefix='items',
                initial=initial
            )

        # Set the request on each form in the formset
        for form in formset_instance:
            form.request = self.request

        formset_instance.helper = helper
        return formset_instance

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.formset is None:
            self.formset = self.get_formset()

        # Ensure management form data is correct
        if self.object and not self.request.POST:  # If this is an update view
            total_forms = self.object.goodsreceivednoteitem_set.all().count()
            self.formset.management_form.initial['TOTAL_FORMS'] = total_forms
            self.formset.management_form.initial['INITIAL_FORMS'] = total_forms

        context['formset'] = self.formset
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['formset']  # Get formset with helper

        if formset.is_valid():
            # Save the main GoodsReceivedNote object
            goods_received_note_object = form.save()
            # Store the GoodsReceivedNote object in self.object
            self.object = goods_received_note_object
            formset.instance = goods_received_note_object

            # To handle delete grn items
            goods_received_note_items = goods_received_note_object.goodsreceivednoteitem_set.all().all()
            updated_items = []

            for each_formset in formset:
                updated_items.append(each_formset.cleaned_data.get("item_id"))

            for item in goods_received_note_items:
                if item.item.id not in updated_items:
                    item.delete()

            # Set request and parent form data on each form in the formset
            for form_item in formset:
                if hasattr(form_item, 'instance') and form_item.instance:
                    # Only set request if the form has an instance
                    form_item.request = self.request

            # Save the formset but store the result in a separate variable
            formset.save()

            return super().form_valid(form)
        else:
            return super().form_invalid(form)

    def form_invalid(self, form):
        """Handle invalid main form or formset."""
        # Ensure the invalid formset (with helper) is put back into the context
        if self.formset is None or not hasattr(self.formset, 'helper'):
            self.formset = self.get_formset()  # Recreate formset with helper
        elif not hasattr(self.formset, 'helper'):
            self.formset.helper = self.get_formset().helper  # Re-attach helper

        return super().form_invalid(form)


class GoodsReceivedNoteCreateAndUpdateMixin:
    """Mixin for GoodsReceivedNote create and update."""

    model = GoodsReceivedNote
    form_class = GoodsReceivedNoteForm
    template_name = "receives/goods_received_note_form.html"  # Ensure this template renders the formset
    cancel_url = reverse_lazy("receives:goods_received_notes:list")

    def get_initial(self):
        initial = super().get_initial()
        # Only set issued_by for new objects (CreateView)
        if self.object is None:
            initial["issued_by"] = self.request.user
        return initial


class GoodsReceivedNoteCreateView(GoodsReceivedNoteFormsetMixin, GoodsReceivedNoteCreateAndUpdateMixin, CoreCreateView):
    """View to create a new GoodsReceivedNote with its items."""
    section_title = "Create GRN"
    submit_text = "Save"
    success_url = "receives:goods_received_notes:panel"
    cancel_url = "receives:goods_received_notes:list"

    def get(self, request, *args, **kwargs):

        # try to check if URL's PARAM exist any wro_pk, if yes, it should be "return" from a specific WRO/DO
        if self.request.GET.get("wro_pk", None):
            if not self.request.user.is_superuser and not self.request.user.groups.filter(name="Superadmin").exists():
                messages.error(self.request, "You do not have permission to perform this action.")
                return redirect(reverse("receives:goods_received_notes:create"))

            try:
                self.warehouse_release_order = WarehouseReleaseOrder.objects.get(pk=self.request.GET.get("wro_pk"))
            except WarehouseReleaseOrder.DoesNotExist:
                messages.error(self.request, "Warehouse Release Order does not exist.")
                return redirect(reverse("receives:goods_received_notes:create"))

            if self.warehouse_release_order.status != WarehouseReleaseOrder.Status.COMPLETED:
                messages.error(self.request, "Warehouse Release Order is not Completed.")
                return redirect(reverse("receives:goods_received_notes:create"))

            if self.warehouse_release_order.is_reverse_pgi is True:
                messages.error(self.request, "Warehouse Release Order is already returned.")
                return redirect(reverse("receives:goods_received_notes:create"))

        return super().get(request, *args, **kwargs)

    def get_form_kwargs(self):
        """
        To make sure the return of GoodsReceivedNote Obj from a WRO have the correct warehouse_release_order
        """
        kwargs = super().get_form_kwargs()
        kwargs["warehouse_release_order"] = getattr(self, "warehouse_release_order", None)
        return kwargs

    def get_initial(self, *args, **kwargs):
        initial = super().get_initial(*args, **kwargs)

        # Pre-populate data if request is a "Return" from WRO
        if getattr(self, "warehouse_release_order", None):
            # initial["consignor"] = self.warehouse_release_order.consignee.consignor
            initial["deliver_to"] = self.warehouse_release_order.warehouses.order_by("-path").first()
            initial["customer_reference"] = self.warehouse_release_order.customer_reference
            initial["remark"] = f"From {self.warehouse_release_order.system_number}"

        return initial

    def form_valid(self, form):
        response = super().form_valid(form)

        # if url PARAM exist wro_pk and reached form_valid, meaning return(GoodsReceivedNote Obj) is created,
        # Mark this WRO/DO's is_return to True
        wro_pk = self.request.POST.get("wro_pk", None)
        if wro_pk:
            warehouse_release_order = WarehouseReleaseOrder.objects.filter(pk=wro_pk)
            if warehouse_release_order.exists():
                wro = warehouse_release_order.first()
                wro.is_return = True
                wro.save()

        return response


goods_received_note_create_view = GoodsReceivedNoteCreateView.as_view()


class GoodsReceivedNoteUpdateView(GoodsReceivedNoteFormsetMixin, GoodsReceivedNoteCreateAndUpdateMixin, CoreUpdateView):
    """View to update an existing GoodsReceivedNote and its items."""
    section_title = "Update GRN"
    submit_text = "Update"
    success_url = "receives:goods_received_notes:panel"

    def get(self, request, *args, **kwargs):
        self.object = self.get_object()
        if self.object.status != GoodsReceivedNote.Status.NEW:
            messages.error(self.request, "Only New GRN can be edited")
            return redirect(reverse("receives:goods_received_notes:list"))

        # Check if all GRN items are in 'Open' status
        items_not_in_open_status = self.object.goodsreceivednoteitem_set.all().exclude(
            status=GoodsReceivedNoteItem.Status.OPEN)
        if items_not_in_open_status.exists():
            messages.error(self.request, "GRN can only be updated when all items are in 'Open' status")
            return redirect(reverse("receives:goods_received_notes:panel", kwargs={"pk": self.object.pk}))

        return super().get(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        self.object = self.get_object()
        if self.object.status != GoodsReceivedNote.Status.NEW:
            messages.error(self.request, "Only New GRN can be edited")
            return redirect(reverse("receives:goods_received_notes:list"))

        # Check if all GRN items are in 'Open' status
        items_not_in_open_status = self.object.goodsreceivednoteitem_set.all().exclude(
            status=GoodsReceivedNoteItem.Status.OPEN)
        if items_not_in_open_status.exists():
            messages.error(self.request, "GRN can only be updated when all items are in 'Open' status")
            return redirect(reverse("receives:goods_received_notes:panel", kwargs={"pk": self.object.pk}))

        return super().post(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        self.formset.extra = 0
        return context


goods_received_note_update_view = GoodsReceivedNoteUpdateView.as_view()


class GoodsReceivedNoteDetailHomeView(CoreDetailView):
    """View for displaying the details of a GRN."""
    model = GoodsReceivedNote
    template_name = "receives/mains/home.html"
    context_object_name = "goods_received_note"


class GoodsReceivedNoteDetailView(CoreDetailView):
    """View for displaying the details of a GRN."""
    model = GoodsReceivedNote
    template_name = "receives/partials/detail.html"
    context_object_name = "goods_received_note"


class GoodsReceivedNoteDataTableDetailView(CoreDataTableDetailView):
    """View that combines a detail view with a data table for GRN."""
    model = GoodsReceivedNote
    table_class = GoodsReceivedNoteDetailTable
    context_object_name = "goods_received_note"
    partial_view = GoodsReceivedNoteDetailHomeView
    search_fields = ["system_number", "status"]
    template_name = 'cores/datatable_detail_view.html'


class GoodsReceivedNoteItemListView(ExportTableMixin, CoreSingleTableView):
    """View for displaying and managing the list of GRN Items."""
    model = GoodsReceivedNoteItem
    table_class = GoodsReceivedNoteItemTable
    template_name = "cores/datatable_detail_view_table.html"
    partial_template_name = "cores/table_partial.html"

    # Search configuration
    search_fields = [
        "goods_received_note__system_number",
        "item__code",
        "item__name",
        "batch_no",
    ]

    # Export configuration
    export_name = "goods_received_note_items"
    export_permission = []  # Empty list means no specific permissions required

    htmx_target = 'table-content-partial'

    def get_queryset(self):
        """Filter items by the grn if specified."""
        queryset = super().get_queryset()
        return queryset.filter(goods_received_note_id=self.kwargs['pk'])

    def get_htmx_base_url(self):
        """
        Override to provide the dynamic URL based on the current item's PK.
        This URL will be used for all HTMX requests and querystring operations.
        """
        pk = self.kwargs.get('pk')
        if pk:
            return reverse("receives:goods_received_notes:item_list", kwargs={"pk": pk})
        return None


def goods_received_note_obsolete_form(request, pk):
    """
    Display the form for marking a grn as obsolete.
    """
    goods_received_note = get_object_or_404(GoodsReceivedNote, pk=pk)

    # Check if the grn is in a status that allows marking as obsolete
    allowed_statuses = [
        GoodsReceivedNote.Status.NEW,
    ]

    if goods_received_note.status not in allowed_statuses:
        messages.error(request,
                       _("Only GRN in 'New', 'Processing', or 'Ready To Print' status can be marked as obsolete."))
        return HttpResponse(status=400, content="GRN not eligible for obsolete action.")

    context = {
        'goods_received_note': goods_received_note,
        'request': request,
    }

    return render(request, 'receives/partials/goods_received_note_obsolete_form.html', context)


@require_POST
def goods_received_note_obsolete(request, pk):
    """
    Mark a grn as obsolete.
    """
    goods_received_note = get_object_or_404(GoodsReceivedNote, pk=pk)

    # Check if the grn is in a status that allows marking as obsolete
    allowed_statuses = [
        GoodsReceivedNote.Status.NEW,
    ]

    if goods_received_note.status not in allowed_statuses:
        messages.error(request,
                       _("Only GRN in 'New', 'Processing', or 'Ready To Print' status can be marked as obsolete."))
        return HttpResponse(status=400, content="GRN not eligible for obsolete action.")

    # Update the grn status
    goods_received_note.status = GoodsReceivedNote.Status.OBSOLETE
    goods_received_note.save(update_fields=['status'])

    # For HTMX requests, return success response
    if request.headers.get('HX-Request'):
        trigger_data = {
            "closeModalEvent": None,
            "showNotificationEvent": {
                "message": "GRN marked as obsolete successfully!",
                "type": "success"
            },
            # Add a refresh trigger to reload the current page
            "refreshPage": True
        }

        headers = {
            'HX-Trigger': json.dumps(trigger_data)
        }
        return HttpResponse(status=200, headers=headers)

    # For non-HTMX requests, redirect back to the detail page
    messages.success(request, _("GRN marked as obsolete successfully."))
    return redirect(reverse("receives:goods_received_notes:panel", kwargs={"pk": pk}))


def goods_received_note_delete_form(request, pk):
    """
    Display the form for deleting a grn.
    """
    goods_received_note = get_object_or_404(GoodsReceivedNote, pk=pk)

    # Check if the grn is in a status that allows deletion
    allowed_statuses = [
        GoodsReceivedNote.Status.NEW,
    ]

    if goods_received_note.status not in allowed_statuses:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Only GRN in 'New', 'Processing', or 'Ready To Print' status can be deleted.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request,
                       _("Only GRN in 'New', 'Processing', or 'Ready To Print' status can be deleted."))
        return HttpResponse(status=400, content="GRN not eligible for deletion.")

    context = {
        'goods_received_note': goods_received_note,
        'request': request,
    }

    return render(request, 'receives/partials/goods_received_note_delete_form.html', context)


@require_POST
def goods_received_note_delete(request, pk):
    """
    Delete a grn.
    """
    goods_received_note = get_object_or_404(GoodsReceivedNote, pk=pk)

    # Check if the grn is in a status that allows deletion
    allowed_statuses = [
        GoodsReceivedNote.Status.NEW,
    ]

    if goods_received_note.status not in allowed_statuses:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Only GRN in 'New', 'Processing', or 'Ready To Print' status can be deleted.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request,
                       _("Only GRN in 'New', 'Processing', or 'Ready To Print' status can be deleted."))
        return HttpResponse(status=400, content="GRN not eligible for deletion.")

    try:
        # Attempt to delete the grn
        goods_received_note.delete()

        # For HTMX requests, return success response
        if request.headers.get('HX-Request'):
            trigger_data = {
                "closeModalEvent": None,
                "showNotificationEvent": {
                    "message": "GRN deleted successfully!",
                    "type": "success"
                },
                # Redirect to the list page after deletion
                "redirectEvent": {"url": reverse("receives:goods_received_notes:list")}
            }

            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            return HttpResponse(status=200, headers=headers)

        # For non-HTMX requests, redirect to the list page
        messages.success(request, _("GRN deleted successfully."))
        return redirect(reverse("receives:goods_received_notes:list"))

    except Exception as e:
        # Handle validation errors or other exceptions
        error_message = str(e)

        # For HTMX requests, return error response
        if request.headers.get('HX-Request'):
            trigger_data = {
                "closeModalEvent": None,
                "showNotificationEvent": {
                    "message": f"Failed to delete GRN: {error_message}",
                    "type": "error"
                }
            }

            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            return HttpResponse(status=400, headers=headers)

        # For non-HTMX requests, redirect back with error message
        messages.error(request, _("Failed to delete GRN: %(error)s") % {'error': error_message})
        return redirect(reverse("receives:goods_received_notes:panel", kwargs={"pk": pk}))


#############################################################
# Old Code, to be remove after everything is stabled.
#############################################################


# import os
# from collections import OrderedDict
# from decimal import Decimal
# from typing import Any

# from django.conf import settings
# from django.contrib import messages
# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.core.files.storage import FileSystemStorage
# from django.db.models import BLANK_CHOICE_DASH, Sum
# from django.db.models.query import QuerySet
# from django.shortcuts import redirect, render
# from django.urls import reverse, reverse_lazy
# from django.utils.translation import gettext_lazy as _

# from actstream.models import Action
# from django_filters.views import FilterView
# from django_tables2 import SingleTableMixin
# from django_tables2.export.views import ExportMixin
# from formtools.wizard.views import CookieWizardView

# from wss.cores.actstream import merge_actstream_to_parent, query_actstream
# from wss.cores.utils import generate_qr, send_notification
# from wss.cores.views import (
#     CoreBaseHistoryModifiedView,
#     CoreCreateView,
#     CoreDataTablesView,
#     CoreDeleteView,
#     CoreDetailDataTablesView,
#     CoreDetailView,
#     CoreListView,
#     CoreUpdateView,
# )
# from wss.cores.views.mixins import HeaderMenuMixin

# from wss.apps.inventories.models import Item
# from wss.apps.releases.models import WarehouseReleaseOrder

# from ..filters import GoodsReceivedNoteFilter, GoodsReceivedNoteHistoryFilter
# from ..forms import (
#     GoodsReceivedNoteContainerUpdateForm,
#     GoodsReceivedNoteForm,
#     GoodsReceivedNoteImportStep1Form,
#     GoodsReceivedNoteImportStep2Form,
#     GoodsReceivedNoteInfoUpdateForm,
#     GoodsReceivedNoteItemExistingImportFormSet,
#     GoodsReceivedNoteItemInlineFormSet,
#     GoodsReceivedNoteItemNewImportFormSet,
#     GoodsReceivedNoteObsoleteForm,
# )
# from ..models import GoodsReceivedNote, GoodsReceivedNoteDefectStockIn, GoodsReceivedNoteItem, GoodsReceivedNoteStockIn
# from ..tables import (
#     GoodsReceivedNoteDataTables,
#     GoodsReceivedNoteDefectDataTables,
#     GoodsReceivedNoteDetailDataTables,
#     GoodsReceivedNoteHistoryDataTables,
# )


# class GoodsReceivedNoteFormsetValid:
#     """GRN formset validation action."""

#     formset_class = GoodsReceivedNoteItemInlineFormSet

#     def update_posted_data(self, posted_data):
#         """Function to update inline item's consignor pk."""

#         modified_data = posted_data.copy()
#         total_inline = int(modified_data["goodsreceivednoteitem_set-TOTAL_FORMS"])

#         for x in range(0, total_inline):
#             modified_data[f"goodsreceivednoteitem_set-{x}-consignor_pk"] = modified_data["consignor"]

#         return modified_data

#     def post(self, request, **kwargs):
#         request.POST = self.update_posted_data(request.POST)
#         return super().post(request, **kwargs)

#     def form_valid(self, form):
#         context = self.get_context_data()
#         formset = context["goods_received_note_item_formset"]

#         if formset.is_valid():
#             # To determine if it is creating a new formset or updating an existing formset
#             new_formset = formset.instance._state.adding

#             self.object = form.save()
#             formset.instance = self.object

#             # To handle delete grn items
#             goods_received_note_items = self.object.goodsreceivednoteitem_set.all()
#             updated_items = []

#             for each_formset in formset:
#                 updated_items.append(each_formset.cleaned_data.get("id"))

#             for item in goods_received_note_items:
#                 if item not in updated_items:
#                     item.delete()

#             instances = formset.save()

#             if new_formset:
#                 merge_actstream_to_parent(instances=instances, parent=self.object)

#             return super().form_valid(form)
#         else:
#             return super().form_invalid(form)


# class GoodsReceivedNoteListView(
#     LoginRequiredMixin, PermissionRequiredMixin, FilterView, SingleTableMixin, CoreListView
# ):
#     """Page to show all GoodsReceivedNote. This page use DataTables server side."""

#     model = GoodsReceivedNote
#     template_name = "receives/goods_received_notes/list.html"
#     table_class = GoodsReceivedNoteDataTables
#     filterset_class = GoodsReceivedNoteFilter

#     # To prevent query all object as it will use ajax call in template
#     object_list = GoodsReceivedNote.objects.none()

#     header_title = "Goods Received Notes"
#     selected_page = "goods_received_notes"
#     selected_subpage = None

#     permission_required = ("receives.view_goodsreceivednote",)

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list


# goods_received_note_list_view = GoodsReceivedNoteListView.as_view()


# class GoodsReceivedNoteDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailDataTablesView):
#     """Page to display selected GoodsReceivedNote based on given GoodsReceivedNote's pk.
#     This page use DataTables server side."""

#     model = GoodsReceivedNote
#     template_name = "receives/goods_received_notes/detail.html"

#     table_class = GoodsReceivedNoteDetailDataTables

#     header_title = "Goods Received Notes"
#     selected_page = "goods_received_notes"
#     selected_subpage = None

#     permission_required = ("receives.view_goodsreceivednote",)

#     def get_queryset(self) -> QuerySet[Item]:
#         """
#         Need to overwrite get_queryset in order to fix CoreDetailDataTablesView.get_selected_object_index function.
#         """
#         user_warehouse = []
#         user = self.request.user
#         user_warehouse = list(user.warehouses.all().values_list("pk", flat=True))

#         # filter GRN base on warehouse and also user permission on warehouse
#         return self.model.objects.filter(deliver_to__pk__in=user_warehouse)

#     def get_total_rejected_quantity(self):
#         return GoodsReceivedNoteDefectStockIn.objects.filter(
#             goods_received_note_item__goods_received_note__pk=self.kwargs["pk"]
#         ).aggregate(Sum("approved_quantity")).get("approved_quantity__sum", Decimal("0")) or Decimal("0")

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         # Round to 0 because currently defect list always use EA
#         context["total_rejected_quantity"] = round(self.get_total_rejected_quantity(), 0)
#         return context


# goods_received_note_detail_view = GoodsReceivedNoteDetailView.as_view()


# class GoodsReceivedNoteCreateAndUpdateMixin:
#     """Mixin for Create and Update."""

#     model = GoodsReceivedNote
#     form_class = GoodsReceivedNoteForm
#     template_name = "receives/goods_received_notes/create_or_update.html"

#     selected_page = "goods_received_notes"
#     selected_subpage = None

#     def get_initial(self):
#         initial = super().get_initial()

#         if self.object is None:
#             initial["issued_by"] = self.request.user

#         return initial

#     def get_success_url(self):
#         return reverse("receives:goods_received_notes:detail", kwargs={"pk": self.object.pk})

#     def get_success_message(self, cleaned_data):
#         return self.success_message % {"numbering": self.object.numbering}


# class GoodsReceivedNoteCreateView(
#     GoodsReceivedNoteCreateAndUpdateMixin,
#     LoginRequiredMixin,
#     PermissionRequiredMixin,
#     GoodsReceivedNoteFormsetValid,
#     CoreCreateView,
# ):
#     """Page to create GoodsReceivedNote."""

#     success_message = _("Receive Note %(numbering)s successfully created")

#     header_title = "New Goods Received Note"

#     permission_required = ("receives.add_goodsreceivednote",)

#     def handle_send_notification(self):
#         send_notification(
#             instance=self.object,
#             message="NEW GRN created. Ready to check!",
#             user_role_list=["Checker"],
#             level="info",
#         )

#     def form_valid(self, form):
#         """If both the form including formset is valid, then only create the GRN object."""
#         context = self.get_context_data()
#         formset = context["goods_received_note_item_formset"]

#         if formset.is_valid():
#             self.object = form.save()
#             # handle notification
#             self.handle_send_notification()

#             wro_pk = self.request.POST.get("wro_pk", None)
#             if wro_pk:
#                 warehouse_release_order = WarehouseReleaseOrder.objects.filter(pk=wro_pk)
#                 if warehouse_release_order.exists():
#                     wro = warehouse_release_order.first()
#                     wro.is_return = True
#                     wro.save()

#             return super().form_valid(form)
#         else:
#             return super().form_invalid(form)

#     def get(self, request, *args, **kwargs):
#         if self.request.GET.get("wro_pk", None):
#             try:
#                 self.warehouse_release_order = WarehouseReleaseOrder.objects.get(pk=self.request.GET.get("wro_pk"))
#             except WarehouseReleaseOrder.DoesNotExist:
#                 messages.error(self.request, "Warehouse Release Order does not exist.")
#                 return redirect(reverse("receives:goods_received_notes:create"))

#             if self.warehouse_release_order.status != WarehouseReleaseOrder.Status.COMPLETED:
#                 messages.error(self.request, "Warehouse Release Order is not Completed.")
#                 return redirect(reverse("receives:goods_received_notes:create"))

#             if self.warehouse_release_order.is_return is True:
#                 messages.error(self.request, "A return has been made for this Warehouse Release Order.")
#                 return redirect(reverse("receives:goods_received_notes:create"))
#         return super().get(request, *args, **kwargs)

#     def get_form_kwargs(self):
#         kwargs = super().get_form_kwargs()
#         kwargs["warehouse_release_order"] = getattr(self, "warehouse_release_order", None)
#         return kwargs

#     def get_initial(self, *args, **kwargs):
#         initial = super().get_initial(*args, **kwargs)

#         # Pre-populate data if request is a Return from WRO
#         if getattr(self, "warehouse_release_order", None):
#             initial["consignor"] = self.warehouse_release_order.consignee.consignor
#             initial["deliver_to"] = self.warehouse_release_order.warehouses.order_by("-path").first()
#             initial["customer_reference"] = self.warehouse_release_order.customer_reference
#             initial["remark"] = f"Return from {self.warehouse_release_order.numbering}"

#         return initial

#     def get_initial_items(self, warehouse_release_order: WarehouseReleaseOrder = None) -> list[dict]:
#         """"""
#         items = []

#         if warehouse_release_order:
#             for wro_item in warehouse_release_order.warehouse_release_order_items.all():
#                 items.append(
#                     {
#                         "item": wro_item.item,
#                         "quantity": wro_item.quantity,
#                         "batch_no": wro_item.batch_no,
#                         "expiry_date": wro_item.expiry_date,
#                         "uom": wro_item.uom,
#                     }
#                 )

#         return items

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)

#         if getattr(self, "warehouse_release_order", None):
#             self.initial_items = self.get_initial_items(warehouse_release_order=self.warehouse_release_order)

#         if self.request.POST:
#             goods_received_note_item_formset = self.formset_class(self.request.POST)
#         else:
#             goods_received_note_item_formset = self.formset_class(initial=getattr(self, "initial_items", []))
#             goods_received_note_item_formset.extra = max(len(getattr(self, "initial_items", [])), 1)

#         context["goods_received_note_item_formset"] = goods_received_note_item_formset
#         context["formset_title"] = context[
#             "goods_received_note_item_formset"
#         ].model._meta.verbose_name_plural.capitalize()
#         context["blank_choice_dash"] = BLANK_CHOICE_DASH[0][1]

#         # To pre-populate selected GRN Items (in case form is invalid and has to redirect back to CreateView page)
#         item_pk_arr, item_display_arr = [], []
#         post_data = self.request.POST

#         if post_data:
#             for key, value in post_data.items():
#                 if key.endswith("-item") and value != "":
#                     item_pk_arr.append(value)
#                     item = Item.objects.get(pk=value)
#                     item_display_arr.append(f"{item.code} :: {item.name}")
#         elif getattr(self, "initial_items", None):
#             # Ensure item fields are also pre-populated if initial_items contains data
#             for grn_item in self.initial_items:
#                 item_pk_arr.append(grn_item["item"].pk)
#                 item_display_arr.append(f"{grn_item['item'].code} :: {grn_item['item'].name}")

#         context["item_pk_arr"] = item_pk_arr
#         context["item_display_arr"] = item_display_arr

#         return context


# goods_received_note_create_view = GoodsReceivedNoteCreateView.as_view()


# class GoodsReceivedNoteUpdateView(
#     GoodsReceivedNoteCreateAndUpdateMixin,
#     LoginRequiredMixin,
#     PermissionRequiredMixin,
#     GoodsReceivedNoteFormsetValid,
#     CoreUpdateView,
# ):
#     """Page to update selected GoodsReceivedNote based on given GoodsReceivedNote's pk."""

#     success_message = _("GoodsReceivedNote %(numbering)s successfully updated")

#     header_title = "Update Goods Received Note"

#     permission_required = ("receives.change_goodsreceivednote",)

#     def handle_send_notification(self):
#         send_notification(
#             instance=self.object,
#             message="had been updated",
#             user_role_list=["Checker"],
#             level="info",
#         )

#     def form_valid(self, form):
#         """If the form is valid, save the associated model."""
#         self.object = form.save()
#         # handle notification
#         self.handle_send_notification()
#         return super().form_valid(form)

#     def get(self, request, *args, **kwargs):
#         self.object = self.get_object()

#         if self.object.status != GoodsReceivedNote.Status.NEW:
#             messages.error(self.request, f"{self.object.get_status_display()} Goods Received Note cannot be edited")
#             return redirect(reverse("receives:goods_received_notes:list"))

#         return super().get(request, *args, **kwargs)

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)

#         if self.request.POST:
#             goods_received_note_item_formset = self.formset_class(self.request.POST, instance=self.object)
#         else:
#             goods_received_note_item_formset = self.formset_class(instance=self.object)

#         goods_received_note_item_formset.extra = 0

#         context["goods_received_note_item_formset"] = goods_received_note_item_formset
#         context["formset_title"] = context[
#             "goods_received_note_item_formset"
#         ].model._meta.verbose_name_plural.capitalize()
#         context["blank_choice_dash"] = BLANK_CHOICE_DASH[0][1]

#         # To pre-populate selected WRO Items
#         item_pk_arr, item_display_arr = [], []

#         for grn_item in self.object.goodsreceivednoteitem_set.all():
#             item_pk_arr.append(grn_item.item.pk)
#             item_display_arr.append(f"{grn_item.item.code} :: {grn_item.item.name}")

#         context["item_pk_arr"] = item_pk_arr
#         context["item_display_arr"] = item_display_arr

#         return context


# goods_received_note_update_view = GoodsReceivedNoteUpdateView.as_view()


# class GoodsReceivedNoteDeleteView(LoginRequiredMixin, PermissionRequiredMixin, CoreDeleteView):
#     """Page to delete selected GoodsReceivedNote based on given GoodsReceivedNote's pk."""

#     model = GoodsReceivedNote
#     success_url = reverse_lazy("receives:goods_received_notes:list")
#     success_message = _("Goods Received Note %(numbering)s successfully deleted")

#     permission_required = ("receives.delete_goodsreceivednote",)

#     def handle_send_notification(self):
#         send_notification(
#             instance=self.object,
#             message=f"{self.object.numbering} had been deleted",
#             user_role_list=["Superadmin", "Admin"],
#             level="warning",
#         )

#     def get(self, request, *args, **kwargs):
#         """TEMPORARY DISABLE DELETE"""

#         self.object = self.get_object()

#         if self.object:
#             messages.error(self.request, "Delete workflow is not define yet")

#         return redirect(reverse("receives:goods_received_notes:list"))

#     def delete(self, request, *args, **kwargs):
#         messages.error(self.request, "Delete workflow is not define yet")

#         # Special handle for HTMX
#         if request.headers.get("hx-request") == "true":
#             return render(request, "partials/_alert.html")

#         self.handle_send_notification()
#         return redirect(reverse("receives:goods_received_notes:list"))


# goods_received_note_delete_view = GoodsReceivedNoteDeleteView.as_view()


# class GoodsReceivedNoteObsoleteView(
#     LoginRequiredMixin,
#     PermissionRequiredMixin,
#     CoreUpdateView,
# ):
#     """
#     Page to obsolete selected GoodsReceivedNote based on given GoodsReceivedNote's pk.

#     Only for following GRN's status and if user has "Superadmin" permission:
#     - NEW
#     """

#     model = GoodsReceivedNote
#     form_class = GoodsReceivedNoteObsoleteForm
#     template_name = "receives/goods_received_notes/create_or_update.html"

#     selected_page = "receives"
#     selected_subpage = "goods_received_notes"

#     success_message = _("Goods Received Note %(numbering)s successfully obsoleted")
#     header_title = "Obsolete a Goods Received Note"

#     permission_required = ("receives.change_goodsreceivednote",)

#     def get_initial(self):

#         initial = super().get_initial()
#         initial["status"] = GoodsReceivedNote.Status.OBSOLETE

#         return initial

#     def get_success_url(self) -> str:
#         return reverse("receives:goods_received_notes:detail", kwargs={"pk": self.object.pk})

#     def get_success_message(self, cleaned_data) -> str:
#         return self.success_message % {"numbering": self.object.numbering}


# goods_received_note_obsolete_view = GoodsReceivedNoteObsoleteView.as_view()


# class GoodsReceivedNotePrintQrView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to print QR in A4 paper with selected GoodsReceivedNote's pk detail view."""

#     model = GoodsReceivedNote
#     template_name = "receives/goods_received_notes/qrcode.html"

#     permission_required = ("receives.view_goodsreceivednote",)

#     def get_qr_context(self, context: dict[str, Any]) -> dict[str, Any]:
#         """Update context with QR code."""

#         domain = f"{self.request.scheme}://{self.request.META['HTTP_HOST']}"
#         qr_scan_url = reverse("receives:goods_received_notes:detail", kwargs={"pk": self.object.pk})
#         constructed_url = f"{domain}{qr_scan_url}"

#         context["qr_code"] = generate_qr(constructed_url, box_size=30)
#         context["qr_link"] = constructed_url

#         return context

#     def get_context_data(self, **kwargs) -> dict[str, Any]:
#         context = super().get_context_data(**kwargs)
#         context = self.get_qr_context(context=context)
#         return context


# goods_received_note_print_qr_view = GoodsReceivedNotePrintQrView.as_view()


# ##############
# # FOR WIZARD #
# ##############


# def check_imported_file(wizard):
#     cleaned_data = wizard.get_cleaned_data_for_step("0") or {"imported_file": False}
#     return cleaned_data["imported_file"]


# class GoodsReceivedNoteImportWizardView(
#     LoginRequiredMixin,
#     PermissionRequiredMixin,
#     HeaderMenuMixin,
#     CookieWizardView,
# ):
#     """Page to import GoodsReceivedNote."""

#     header_title = "Import Mitsubishi"
#     selected_page = "goods_received_notes"
#     selected_subpage = None

#     permission_required = ("receives.add_goodsreceivednote",)

#     form_list = [GoodsReceivedNoteImportStep1Form, GoodsReceivedNoteImportStep2Form]
#     template_list = [
#         "receives/goods_received_notes/import/step1.html",
#         "receives/goods_received_notes/import/step2.html",
#     ]
#     condition_dict = {"imported_file": check_imported_file}
#     file_storage = FileSystemStorage(location=os.path.join(settings.MEDIA_ROOT, "GRN_IMPORT_WIZARD"))

#     imported_object = None

#     def handle_send_notification(self):
#         send_notification(
#             instance=self.object,
#             message="NEW GRN created. Ready to check!",
#             user_role_list=["Checker"],
#             level="info",
#         )

#     def get_template_names(self):
#         return [self.template_list[int(self.steps.current)]]

#     def get_form_initial(self, step):
#         if step == "1":
#             form_0_cleaned_data = None
#             form_0_obj = self.get_form(
#                 step="0",
#                 data=self.storage.get_step_data("0"),
#                 files=self.storage.get_step_files("0"),
#             )
#             if form_0_obj.is_valid():
#                 form_0_cleaned_data = form_0_obj.cleaned_data
#                 self.imported_object = form_0_obj.imported_object

#             if form_0_cleaned_data:
#                 return self.initial_dict.get(step, {"customer_reference": self.imported_object.shipment_number})

#         return self.initial_dict.get(step, {})

#     def get_context_data(self, form, **kwargs):
#         context = super().get_context_data(form=form, **kwargs)

#         if self.steps.current == "1":
#             cleaned_data = self.get_cleaned_data_for_step("0")
#             consignor = self.imported_object.consignor
#             item_queryset = consignor.items
#             new_items = self.imported_object.new_items
#             formset_initial = []
#             if new_items:
#                 for key, value in new_items.items():
#                     value["item_name"] = value["item_code"]
#                     formset_initial.append(value)

#             new_formset = GoodsReceivedNoteItemNewImportFormSet(
#                 prefix="new", initial=formset_initial, form_kwargs={"item_queryset": item_queryset}
#             )

#             existing_items = self.imported_object.existing_items
#             formset_initial = []
#             if existing_items:
#                 for key, value in existing_items.items():
#                     formset_initial.append(value)

#             if settings.IMPORT_MITSUBISHI_UPDATE_EXISTING is True:
#                 existing_formset = GoodsReceivedNoteItemExistingImportFormSet(
#                     prefix="existing", initial=formset_initial, form_kwargs={"item_queryset": item_queryset}
#                 )
#             else:
#                 existing_formset = None

#             context.update(
#                 {
#                     "new_items": new_items,
#                     "existing_items": existing_items,
#                     "consignor": consignor,
#                     "total_item": len(self.imported_object.import_line_list),
#                     "deliver_to": cleaned_data.get("deliver_to"),
#                     "new_formset": new_formset,
#                     "existing_formset": existing_formset,
#                 }
#             )

#         return context

#     def render_done(self, form, **kwargs):
#         """
#         This method gets called when all forms passed. The method should also
#         re-validate all steps to prevent manipulation. If any form fails to
#         validate, `render_revalidation_failure` should get called.
#         If everything is fine call `done`.
#         """
#         final_forms = OrderedDict()
#         # walk through the form list and try to validate the data again.
#         for form_key in self.get_form_list():
#             form_obj = self.get_form(
#                 step=form_key, data=self.storage.get_step_data(form_key), files=self.storage.get_step_files(form_key)
#             )
#             if not form_obj.is_valid():
#                 return self.render_revalidation_failure(form_key, form_obj, **kwargs)
#             final_forms[form_key] = form_obj

#         # render the done view and reset the wizard before returning the
#         # response. This is needed to prevent from rendering done with the
#         # same data twice.
#         done_status, done_response = self.done(list(final_forms.values()), form_dict=final_forms, **kwargs)
#         if done_status is True:
#             self.storage.reset()
#             return done_response
#         else:
#             # Hits errors here
#             return self.render(form)

#     def done(self, form_list, **kwargs):
#         """Process form data and use it to create GRN and then return status and redirect URL."""
#         new_formset = GoodsReceivedNoteItemNewImportFormSet(prefix="new", data=self.request.POST)

#         if settings.IMPORT_MITSUBISHI_UPDATE_EXISTING is True:
#             existing_formset = GoodsReceivedNoteItemExistingImportFormSet(prefix="existing", data=self.request.POST)

#         new_items = self.imported_object.new_items
#         existing_items = self.imported_object.existing_items

#         if not new_items:
#             is_new_valid = True
#         else:
#             is_new_valid = new_formset.is_valid()

#         if settings.IMPORT_MITSUBISHI_UPDATE_EXISTING is True:
#             if not existing_items:
#                 is_existing_valid = True
#             else:
#                 is_existing_valid = existing_formset.is_valid()
#         else:
#             is_existing_valid = True

#         if is_new_valid is True and is_existing_valid is True:
#             # To handle when new_formset is empty
#             try:
#                 new_formset_obj = new_formset.cleaned_data
#             except AttributeError:
#                 new_formset_obj = {}

#             if settings.IMPORT_MITSUBISHI_UPDATE_EXISTING is True:
#                 # To handle when existing_formset is empty
#                 try:
#                     existing_formset_obj = existing_formset.cleaned_data
#                 except AttributeError:
#                     existing_formset_obj = {}

#             updated_new_items = {}
#             for item in new_formset_obj:
#                 # Only create GRN item when it's not skip
#                 if item["actions"] != "skip":
#                     if item["actions"] == "new":
#                         remark = "ITEM CREATED FROM IMPORT"
#                         item_or_name = item["item_name"]
#                     elif item["actions"] == "existing":
#                         remark = "ITEM CHOOSEN FROM EXISTING"
#                         item_or_name = item["item"]

#                     updated_new_items[str(int(item["running_number"]))] = {
#                         "item_code": item["item_code"],
#                         "item": item_or_name,
#                         "quantity": str(item["quantity"]),
#                         "uom": item["uom"],
#                         "running_number": item["running_number"],
#                         "shipment_number": item["shipment_number"],
#                         "pallet_number": item["pallet_number"],
#                         "remark": remark,
#                     }

#             if settings.IMPORT_MITSUBISHI_UPDATE_EXISTING is True:
#                 updated_existing_items = {}
#                 for item in existing_formset_obj:
#                     updated_existing_items[str(int(item["running_number"]))] = {
#                         "item_code": item["item"].code,
#                         "item": item["item"],
#                         "quantity": str(item["quantity"]),
#                         "uom": item["uom"],
#                         "running_number": item["running_number"],
#                         "shipment_number": item["shipment_number"],
#                         "pallet_number": item["pallet_number"],
#                         "remark": "",
#                     }

#                 for key, value in updated_existing_items.items():
#                     if value != existing_items[key]:
#                         updated_existing_items[key]["remark"] = "ITEM DIFFERENT FROM IMPORT"

#             # Merge and create final_item_list
#             if settings.IMPORT_MITSUBISHI_UPDATE_EXISTING is True:
#                 final_item_list = updated_new_items | updated_existing_items
#             else:
#                 final_item_list = updated_new_items | existing_items

#             # Preparing data for GRN creation
#             form_1_obj = form_list[0].cleaned_data
#             form_2_obj = form_list[1].cleaned_data
#             deliver_to = form_1_obj.get("deliver_to")
#             customer_reference = form_2_obj.get("customer_reference")
#             arrival_datetime = form_2_obj.get("arrival_datetime")

#             goods_received_note = self.imported_object.process_external(
#                 user=self.request.user,
#                 deliver_to=deliver_to,
#                 customer_reference=customer_reference,
#                 arrival_datetime=arrival_datetime,
#                 final_item_list=final_item_list,
#             )

#             # send notification
#             self.object = goods_received_note
#             self.handle_send_notification()

#             return True, redirect(goods_received_note.get_absolute_url())

#         else:
#             messages.error(self.request, _("There are some errors during import."))

#             for error_messages in new_formset.errors:
#                 for field, errors in error_messages.items():
#                     messages.error(self.request, f"New items: {field}: {','.join(errors)}")

#             if settings.IMPORT_MITSUBISHI_UPDATE_EXISTING is True:
#                 for error_messages in existing_formset.errors:
#                     for field, errors in error_messages.items():
#                         messages.error(self.request, f"Existing items: {field}: {','.join(errors)}")

#         return False, None


# goods_received_note_import_wizard_view = GoodsReceivedNoteImportWizardView.as_view()


# ##################
# # FOR DataTables #
# ##################


# class GoodsReceivedNoteDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in list page."""

#     model = GoodsReceivedNote
#     table_class = GoodsReceivedNoteDataTables
#     filterset_class = GoodsReceivedNoteFilter

#     permission_required = ("receives.view_goodsreceivednote",)

#     def get_queryset(self) -> QuerySet[GoodsReceivedNote]:

#         user_warehouse = []
#         user = self.request.user
#         user_warehouse = list(user.warehouses.all().values_list("pk", flat=True))

#         # filter GRN base on warehouse and also user permission on warehouse
#         return self.model.objects.filter(deliver_to__pk__in=user_warehouse)


# goods_received_note_datatables_view = GoodsReceivedNoteDataTablesView.as_view()


# class GoodsReceivedNoteDetailDataTablesView(GoodsReceivedNoteDataTablesView):
#     """JSON for DataTables in detail page."""

#     table_class = GoodsReceivedNoteDetailDataTables


# goods_received_note_detail_datatables_view = GoodsReceivedNoteDetailDataTablesView.as_view()


# class GoodsReceivedNoteHistoryDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in GoodsReceivedNote History's page."""

#     model = Action
#     table_class = GoodsReceivedNoteHistoryDataTables
#     filterset_class = GoodsReceivedNoteHistoryFilter
#     permission_required = ("receives.view_goodsreceivednote",)

#     def get_goods_received_note(self) -> GoodsReceivedNote:
#         goods_received_note = (
#             GoodsReceivedNote.objects.select_related("created_by", "issued_by", "consignor", "deliver_to")
#             .prefetch_related("goodsreceivednoteitem_set")
#             .get(pk=self.kwargs["pk"])
#         )
#         return goods_received_note

#     def get_goods_received_note_item(self) -> QuerySet[GoodsReceivedNoteItem]:
#         return self.goods_received_note.goodsreceivednoteitem_set.all()

#     def get_goods_received_note_stock_in(self) -> QuerySet[GoodsReceivedNoteStockIn]:
#         return GoodsReceivedNoteStockIn.objects.filter(
#             goods_received_note_item__goods_received_note=self.goods_received_note
#         )

#     def get_queryset(self) -> QuerySet[Action]:
#         self.goods_received_note = self.get_goods_received_note()
#         action_goods_received_note_qs = query_actstream(target=self.goods_received_note)
#         action_goods_received_note_stock_in_qs = query_actstream(target=self.get_goods_received_note_stock_in())

#         action_qs = action_goods_received_note_qs | action_goods_received_note_stock_in_qs

#         return action_qs


# goods_received_note_history_list_datatables_view = GoodsReceivedNoteHistoryDataTablesView.as_view()


# ############
# # FOR HTMX #
# ############


# class GoodsReceivedNoteInfoDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected GoodsReceivedNote's info based on given GoodsReceivedNote's pk."""

#     model = GoodsReceivedNote
#     template_name = "receives/goods_received_notes/partials/htmx/_info.html"

#     permission_required = ("receives.view_goodsreceivednote",)


# goods_received_note_info_detail_view = GoodsReceivedNoteInfoDetailView.as_view()


# class GoodsReceivedNoteInfoUpdateView(LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Partial page to update selected GoodsReceivedNote's info based on given GoodsReceivedNote's pk."""

#     model = GoodsReceivedNote
#     form_class = GoodsReceivedNoteInfoUpdateForm
#     template_name = "receives/goods_received_notes/partials/htmx/_info_form.html"
#     success_message = _("GoodsReceivedNote basic information successfully updated")

#     permission_required = ("receives.change_goodsreceivednote",)

#     def handle_send_notification(self):
#         send_notification(
#             instance=self.object,
#             message="had been updated",
#             user_role_list=["Checker"],
#             level="info",
#         )

#     def form_valid(self, form):
#         """If the form is valid, save the associated model."""
#         self.object = form.save()
#         # handle notification
#         self.handle_send_notification()
#         return super().form_valid(form)

#     def get_success_url(self):
#         return reverse("receives:goods_received_notes:info", kwargs={"pk": self.object.pk})


# goods_received_note_info_update_view = GoodsReceivedNoteInfoUpdateView.as_view()


# class GoodsReceivedNoteStatusDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to show GoodsReceivedNote's status."""

#     model = GoodsReceivedNote
#     template_name = "receives/goods_received_notes/partials/htmx/_status.html"

#     permission_required = ("receives.view_goodsreceivednote",)


# goods_received_note_status_detail_view = GoodsReceivedNoteStatusDetailView.as_view()


# class GoodsReceivedNoteIsRackingCompletedDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to show GoodsReceivedNote's status."""

#     model = GoodsReceivedNote
#     template_name = "receives/goods_received_notes/partials/htmx/_is_racking_completed.html"

#     permission_required = ("receives.view_goodsreceivednote",)


# goods_received_note_is_racking_completed_detail_view = GoodsReceivedNoteIsRackingCompletedDetailView.as_view()


# class GoodsReceivedNoteContainerDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected GoodsReceivedNote's container based on given GoodsReceivedNote's pk."""

#     model = GoodsReceivedNote
#     template_name = "receives/goods_received_notes/partials/htmx/_container.html"

#     permission_required = ("receives.view_goodsreceivednote",)


# goods_received_note_container_detail_view = GoodsReceivedNoteContainerDetailView.as_view()


# class GoodsReceivedNoteContainerUpdateView(LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Partial page to update selected GoodsReceivedNote's container based on given GoodsReceivedNote's pk."""

#     model = GoodsReceivedNote
#     form_class = GoodsReceivedNoteContainerUpdateForm
#     template_name = "receives/goods_received_notes/partials/htmx/_container_form.html"
#     success_message = _("Container successfully updated")

#     permission_required = ("receives.change_goodsreceivednote",)

#     def get_success_url(self):
#         return reverse("receives:goods_received_notes:container", kwargs={"pk": self.object.pk})


# goods_received_note_container_update_view = GoodsReceivedNoteContainerUpdateView.as_view()


# class GoodsReceivedNoteDefectsListView(LoginRequiredMixin, PermissionRequiredMixin, SingleTableMixin, CoreListView):
#     """Partial page to show all GoodsReceivedNoteDefect based on given GoodsReceivedNote's pk."""

#     model = GoodsReceivedNoteDefectStockIn
#     table_class = GoodsReceivedNoteDefectDataTables
#     template_name = "receives/goods_received_notes/partials/htmx/_defects.html"

#     permission_required = ("receives.view_goodsreceivednote",)

#     def get_queryset(self):
#         return self.model.objects.filter(goods_received_note_item__goods_received_note__pk=self.kwargs["pk"])

#     def get_total_rejected_quantity(self):
#         return self.object_list.aggregate(Sum("approved_quantity")).get(
#             "approved_quantity__sum", Decimal("0")
#         ) or Decimal("0")

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         # Round to 0 because currently defect list always use EA
#         context["total_rejected_quantity"] = round(self.get_total_rejected_quantity(), 0)
#         return context


# goods_received_note_defects_list_view = GoodsReceivedNoteDefectsListView.as_view()


# class GoodsReceivedNoteItemsListView(LoginRequiredMixin, PermissionRequiredMixin, CoreListView):
#     """Partial page to show all GoodsReceivedNoteItem based on given GoodsReceivedNote's pk."""

#     model = GoodsReceivedNoteItem
#     template_name = "receives/goods_received_notes/partials/htmx/_items.html"

#     permission_required = ("receives.view_goodsreceivednote",)

#     def get_queryset(self):
#         return self.model.objects.filter(goods_received_note__pk=self.kwargs["pk"])


# goods_received_note_items_list_view = GoodsReceivedNoteItemsListView.as_view()


# class GoodsReceivedNoteHistoryListView(LoginRequiredMixin, PermissionRequiredMixin, SingleTableMixin, CoreListView):
#     """
#     Partial page to show all Actstreams based on given GoodsReceivedNote's pk as target_object_id.
#     This page use DataTables server side.
#     """

#     model = Action
#     template_name = "receives/goods_received_notes/partials/htmx/_history.html"
#     table_class = GoodsReceivedNoteHistoryDataTables

#     # To prevent query all object as it will use ajax call in template
#     object_list = Action.objects.none()

#     permission_required = ("receives.view_goodsreceivednote",)

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list

#     def get_goods_received_note(self) -> GoodsReceivedNote:
#         return GoodsReceivedNote.objects.get(pk=self.kwargs["pk"])

#     def get_context_data(self, **kwargs) -> Any:
#         context = super().get_context_data(**kwargs)
#         context["object"] = self.get_goods_received_note()
#         return context


# goods_received_note_history_list_view = GoodsReceivedNoteHistoryListView.as_view()


# class GoodsReceivedNoteHistoryModifiedView(CoreBaseHistoryModifiedView):
#     """
#     Partial pop up view to show the differences in GoodsReceivedNote history view.
#     """

#     permission_required = ("receives.view_goodsreceivednote",)


# goods_received_note_history_modified_view = GoodsReceivedNoteHistoryModifiedView.as_view()


# class ExportAllGoodsReceivedNoteToXlsxView(ExportMixin, GoodsReceivedNoteDataTablesView):
#     """
#     Class-based view (CBV) to export entire filtered queryset without pagination.
#     """

#     # When we export entire/all rows, we disable pagination
#     table_pagination = False

#     def get_export_filename(self, export_format) -> str:
#         """Override filename."""

#         wss = settings.PROJECT_NAME
#         self.export_name = f"{wss} Goods Received Note"

#         return f"{self.export_name}.{export_format}"


# export_all_goods_received_note_to_xlsx_view = ExportAllGoodsReceivedNoteToXlsxView.as_view()
