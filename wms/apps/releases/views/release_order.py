from crispy_forms.helper import <PERSON><PERSON>el<PERSON>
import json
from typing import Any

from django.contrib import messages
from django.db import transaction
from django.db.models import QuerySet
from django.forms import inlineformset_factory, ValidationError
from django.http import HttpResponse
from django.shortcuts import redirect, render, get_object_or_404
from django.urls import reverse_lazy, reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_POST

from wms.cores.mixins import ExportTableMixin
from wms.cores.views import (
    CoreSingleTableView,
    CoreCreateView,
    CoreUpdateView,
    CoreDetailView,
    CoreDataTableDetailView,
    CoreFormView,
    CoreTemplateView,
)

from wms.apps.releases.models import WarehouseReleaseOrder, WarehouseReleaseOrderItem, WarehouseReleaseOrderStockOut
from wms.apps.releases.tables import ReleaseOrderTable, ReleaseOrderDetailTable, ReleaseOrderItemTable
from wms.apps.releases.filters import <PERSON>OrderFilter
from wms.apps.releases.forms.release_order import ReleaseOrderForm, ReleaseOrderItemForm, WroImportBulkEditExcelForm


class ReleaseOrderListView(ExportTableMixin, CoreSingleTableView):
    """
    View for displaying and managing the list of Release Orders.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    - Filtering
    """
    model = WarehouseReleaseOrder
    table_class = ReleaseOrderTable
    template_name = "cores/list_table.html"
    partial_template_name = "cores/table_partial.html"
    filterset_class = ReleaseOrderFilter

    # Search configuration
    search_fields = [
        "system_number",
        "deliveryorder__system_number",
        "customer_reference",
        "consignee__display_name",
        "consignor_picking_list_no",
        "issued_by__username"
    ]

    # Export configuration
    export_name = "release_orders"
    export_permission = []  # Empty list means no specific permissions required

    # Permission configuration
    permission_required = ("releases.view_warehousereleaseorder",)

    # def get_queryset(self) -> QuerySet:
    #     """
    #     Return all warehouse release orders filtered by user's warehouses.
    #     """
    #     queryset = super().get_queryset()
    #     user_warehouse = list(self.request.user.warehouses.all().values_list("pk", flat=True))
    #     return queryset.filter(warehouses__pk__in=user_warehouse).distinct()

    def get_queryset(self):
        queryset = super().get_queryset()

        # Example: only show items related to the user's desired consignor
        consignor_filter = self.request.user.consignor_filter

        if consignor_filter:
            return queryset.filter(consignee__consignor=consignor_filter)
        else:
            return queryset

release_order_list_view = ReleaseOrderListView.as_view()


class ReleaseOrderFormsetMixin:
    """Mixin to handle ReleaseOrderItem inline formset with Crispy Forms (Table Layout)."""
    formset = None

    def get_formset(self):
        """Creates and returns the ReleaseOrderItem formset with a Crispy Helper for table layout."""
        release_order_item_form_set = inlineformset_factory(
            WarehouseReleaseOrder,
            WarehouseReleaseOrderItem,
            form=ReleaseOrderItemForm,
            extra=1,
            validate_min=True,
            # can_delete=True
        )

        # Create the helper for the formset
        helper = FormHelper()
        helper.form_tag = False
        helper.disable_csrf = True
        helper.template = 'tailwind/table_inline_formset.html'
        helper.formset_header_title = ""

        if self.request.method == 'POST':
            formset_instance = release_order_item_form_set(
                self.request.POST,
                self.request.FILES,
                instance=self.object,
                prefix='items'
            )
        else:
            # For update view, set extra=0 if there are existing items
            initial_extra = 0 if (self.object and self.object.warehouse_release_order_items.exists()) else 1
            release_order_item_form_set.extra = initial_extra

            formset_instance = release_order_item_form_set(
                instance=self.object,
                prefix='items'
            )

        # Set the request on each form in the formset
        for form in formset_instance:
            form.request = self.request

        formset_instance.helper = helper
        return formset_instance

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.formset is None:
            self.formset = self.get_formset()

        # Ensure management form data is correct
        if self.object and not self.request.POST:  # If this is an update view
            total_forms = self.object.warehouse_release_order_items.count()
            self.formset.management_form.initial['TOTAL_FORMS'] = total_forms
            self.formset.management_form.initial['INITIAL_FORMS'] = total_forms

        context['formset'] = self.formset
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['formset']  # Get formset with helper

        if formset.is_valid():
            # Save the main ReleaseOrder object
            release_order_object = form.save()
            # Store the ReleaseOrder object in self.object
            self.object = release_order_object
            formset.instance = release_order_object

            # To handle delete release order items
            release_order_items = release_order_object.warehouse_release_order_items.all()
            updated_items = []

            for each_formset in formset:
                updated_items.append(each_formset.cleaned_data.get("item_id"))

            for item in release_order_items:
                if item.item.id not in updated_items:
                    item.delete()

            # Set request and parent form data on each form in the formset
            for form_item in formset:
                if hasattr(form_item, 'instance') and form_item.instance:
                    # Only set request if the form has an instance
                    form_item.request = self.request

            # Save the formset but store the result in a separate variable
            formset.save()

            return super().form_valid(form)
        else:
            return super().form_invalid(form)

    def form_invalid(self, form):
        """Handle invalid main form or formset."""
        # Ensure the invalid formset (with helper) is put back into the context
        if self.formset is None or not hasattr(self.formset, 'helper'):
            self.formset = self.get_formset()  # Recreate formset with helper
        elif not hasattr(self.formset, 'helper'):
            self.formset.helper = self.get_formset().helper  # Re-attach helper

        return super().form_invalid(form)


class ReleaseOrderCreateAndUpdateMixin:
    """Mixin for ReleaseOrder create and update."""

    model = WarehouseReleaseOrder
    form_class = ReleaseOrderForm
    template_name = "releases/release_order_form.html"  # Ensure this template renders the formset
    cancel_url = reverse_lazy("releases:orders:list")

    def get_initial(self):
        initial = super().get_initial()
        # Only set issued_by for new objects (CreateView)
        if self.object is None:
            initial["issued_by"] = self.request.user
        return initial


class ReleaseOrderCreateView(ReleaseOrderFormsetMixin, ReleaseOrderCreateAndUpdateMixin, CoreCreateView):
    """View to create a new ReleaseOrder with its items."""
    section_title = "Create Release Order"
    submit_text = "Save"
    success_url = "releases:orders:panel"
    cancel_url = "releases:orders:list"


release_order_create_view = ReleaseOrderCreateView.as_view()


class ReleaseOrderUpdateView(ReleaseOrderFormsetMixin, ReleaseOrderCreateAndUpdateMixin, CoreUpdateView):
    """View to update an existing ReleaseOrder and its items."""
    section_title = "Update Release Order"
    submit_text = "Update"
    success_url = "releases:orders:panel"

    def get(self, request, *args, **kwargs):
        self.object = self.get_object()
        if self.object.status != WarehouseReleaseOrder.Status.NEW:
            messages.error(self.request, "Only New Release Orders can be edited")
            return redirect(reverse("releases:orders:list"))

        # Check if all order items are in 'New' status
        items_not_in_new_status = self.object.warehouse_release_order_items.exclude(
            status=WarehouseReleaseOrderItem.Status.NEW)
        if items_not_in_new_status.exists():
            messages.error(self.request, "Release Order can only be updated when all items are in 'New' status")
            return redirect(reverse("releases:orders:panel", kwargs={"pk": self.object.pk}))

        return super().get(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        self.object = self.get_object()
        if self.object.status != WarehouseReleaseOrder.Status.NEW:
            messages.error(self.request, "Only New Release Orders can be edited")
            return redirect(reverse("releases:orders:list"))

        # Check if all order items are in 'New' status
        items_not_in_new_status = self.object.warehouse_release_order_items.exclude(
            status=WarehouseReleaseOrderItem.Status.NEW)
        if items_not_in_new_status.exists():
            messages.error(self.request, "Release Order can only be updated when all items are in 'New' status")
            return redirect(reverse("releases:orders:panel", kwargs={"pk": self.object.pk}))

        return super().post(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        self.formset.extra = 0
        return context


release_order_update_view = ReleaseOrderUpdateView.as_view()


class ReleaseOrderDetailHomeView(CoreDetailView):
    """View for displaying the details of a Release Order."""
    model = WarehouseReleaseOrder
    template_name = "releases/mains/home.html"
    context_object_name = "release_order"


class ReleaseOrderDetailView(CoreDetailView):
    """View for displaying the details of a Release Order."""
    model = WarehouseReleaseOrder
    template_name = "releases/partials/detail.html"
    context_object_name = "release_order"


class ReleaseOrderDataTableDetailView(CoreDataTableDetailView):
    """View that combines a detail view with a data table for Release Orders."""
    model = WarehouseReleaseOrder
    table_class = ReleaseOrderDetailTable
    context_object_name = "release_order"
    partial_view = ReleaseOrderDetailHomeView
    search_fields = ["system_number", "status"]
    template_name = "releases/datatable_detail_view.html"  # Use custom template with left panel hidden by default


class ReleaseOrderItemListView(ExportTableMixin, CoreSingleTableView):
    """View for displaying and managing the list of Release Order Items."""
    model = WarehouseReleaseOrderItem
    table_class = ReleaseOrderItemTable
    template_name = "cores/datatable_detail_view_table.html"
    partial_template_name = "cores/table_partial.html"

    # Search configuration
    search_fields = [
        "release_order__system_number",
        "item__code",
        "item__name",
        "batch_no",
    ]

    # Export configuration
    export_name = "release_order_items"
    export_permission = []  # Empty list means no specific permissions required

    htmx_target = 'table-content-partial'

    def get_queryset(self):
        """Filter items by the release order if specified."""
        queryset = super().get_queryset()
        return queryset.filter(release_order_id=self.kwargs['pk'])

    def get_htmx_base_url(self):
        """
        Override to provide the dynamic URL based on the current item's PK.
        This URL will be used for all HTMX requests and querystring operations.
        """
        pk = self.kwargs.get('pk')
        if pk:
            return reverse("releases:orders:item_list", kwargs={"pk": pk})
        return None


def release_order_obsolete_form(request, pk):
    """
    Display the form for marking a release order as obsolete.
    """
    release_order = get_object_or_404(WarehouseReleaseOrder, pk=pk)

    # Check if the release order is in a status that allows marking as obsolete
    allowed_statuses = [
        WarehouseReleaseOrder.Status.NEW,
        WarehouseReleaseOrder.Status.PROCESSING,
        WarehouseReleaseOrder.Status.READY_TO_PRINT
    ]

    if release_order.status not in allowed_statuses:
        messages.error(request,
                       _("Only Release Orders in 'New', 'Processing', or 'Ready To Print' status can be marked as obsolete."))
        return HttpResponse(status=400, content="Release order not eligible for obsolete action.")

    context = {
        'release_order': release_order,
        'request': request,
    }

    return render(request, 'releases/partials/release_order_obsolete_form.html', context)


@require_POST
def release_order_obsolete(request, pk):
    """
    Mark a release order as obsolete.
    """
    release_order = get_object_or_404(WarehouseReleaseOrder, pk=pk)

    # Check if the release order is in a status that allows marking as obsolete
    allowed_statuses = [
        WarehouseReleaseOrder.Status.NEW,
        WarehouseReleaseOrder.Status.PROCESSING,
        WarehouseReleaseOrder.Status.READY_TO_PRINT
    ]

    if release_order.status not in allowed_statuses:
        messages.error(request,
                       _("Only Release Orders in 'New', 'Processing', or 'Ready To Print' status can be marked as obsolete."))
        return HttpResponse(status=400, content="Release order not eligible for obsolete action.")

    # Check if the release order is linked to a PickingList
    if hasattr(release_order, 'picking_list') and release_order.picking_list:
        error_message = _("Cannot mark as obsolete. This Release Order is linked to a Picking List.")
        messages.error(request, error_message)

        # For HTMX requests, return a response that will close the modal and show the error on the detail view
        if request.headers.get('HX-Request'):
            trigger_data = {
                "closeModalEvent": None,
                "showNotificationEvent": {
                    "message": str(error_message),
                    "type": "error"
                },
                "redirectEvent": {"url": reverse("releases:orders:panel", kwargs={"pk": pk})}
            }
            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            return HttpResponse(status=200, headers=headers)

        # For non-HTMX requests, redirect back to the detail page
        return redirect(reverse("releases:orders:panel", kwargs={"pk": pk}))

    # Update the release order status
    release_order.status = WarehouseReleaseOrder.Status.OBSOLETE
    release_order.save(update_fields=['status'])

    # For HTMX requests, return success response
    if request.headers.get('HX-Request'):
        trigger_data = {
            "closeModalEvent": None,
            "showNotificationEvent": {
                "message": "Release order marked as obsolete successfully!",
                "type": "success"
            },
            # Add a refresh trigger to reload the current page
            "refreshPage": True
        }

        headers = {
            'HX-Trigger': json.dumps(trigger_data)
        }
        return HttpResponse(status=200, headers=headers)

    # For non-HTMX requests, redirect back to the detail page
    messages.success(request, _("Release order marked as obsolete successfully."))
    return redirect(reverse("releases:orders:panel", kwargs={"pk": pk}))


@require_POST
def release_order_proceed_print(request, pk):
    """
    Process a release order for printing by creating stock out records for all picked items.
    This changes the status from READY_TO_PRINT to READY_TO_RELEASE.
    """
    release_order = get_object_or_404(WarehouseReleaseOrder, pk=pk)

    # Check if the release order is in READY_TO_PRINT status
    if release_order.status != WarehouseReleaseOrder.Status.READY_TO_PRINT:
        # messages.error(request, _("Only Release Orders in 'Ready To Print' status can be processed for printing."))

        # For HTMX requests, return error response with proper headers
        if request.headers.get('HX-Request'):
            trigger_data = {
                "showNotificationEvent": {
                    "message": str(_("Only Release Orders in 'Ready To Print' status can be processed for printing.")),
                    "type": "error"
                }
            }
            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            return HttpResponse(status=400, headers=headers, content="Release order not eligible for printing action.")

        return HttpResponse(status=400, content="Release order not eligible for printing action.")

    try:
        with transaction.atomic():
            # Loop through all release order items
            for release_order_item in release_order.warehouse_release_order_items.all():
                # Loop through all pickers for each item
                for picker in release_order_item.warehouse_release_order_item_pickers.all():
                    # Create a stock out record for each picker
                    WarehouseReleaseOrderStockOut.objects.create(
                        release_order_item=release_order_item,
                        released_by=request.user,
                        released_quantity=picker.system_quantity,
                        stock_out_datetime=timezone.now(),
                        stock=picker.stock,
                        uom=release_order_item.uom,
                    )

            # Update the release order status to READY_TO_RELEASE
            release_order.status = WarehouseReleaseOrder.Status.READY_TO_RELEASE
            release_order.save(update_fields=['status'])

            # For HTMX requests, return success response
            if request.headers.get('HX-Request'):
                trigger_data = {
                    "showNotificationEvent": {
                        "message": "Release order processed for printing successfully!",
                        "type": "success"
                    },
                    # Add a refresh trigger to reload the current page
                    "refreshPage": True
                }

                headers = {
                    'HX-Trigger': json.dumps(trigger_data)
                }
                return HttpResponse(status=200, headers=headers)

            # For non-HTMX requests, redirect back to the detail page with success message
            messages.success(request, _("Release order processed for printing successfully."))
            return redirect(reverse("releases:orders:panel", kwargs={"pk": pk}))

    except Exception as e:
        # If an error occurs, return an error response
        if request.headers.get('HX-Request'):
            trigger_data = {
                "showNotificationEvent": {
                    "message": _("Failed to process release order for printing: %(error)s") % {'error': str(e)},
                    "type": "error"
                }
            }
            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            return HttpResponse(status=500, headers=headers)

        messages.error(request, _("Failed to process release order for printing: %(error)s") % {'error': str(e)})
        return redirect(reverse("releases:orders:panel", kwargs={"pk": pk}))


@require_POST
def release_order_proceed_release(request, pk):
    """
    Process a release order for final release by updating its status to COMPLETED.
    This changes the status from READY_TO_RELEASE to COMPLETED.
    """
    release_order = get_object_or_404(WarehouseReleaseOrder, pk=pk)

    # Check if the release order is in READY_TO_RELEASE status
    if release_order.status != WarehouseReleaseOrder.Status.READY_TO_RELEASE:
#         messages.error(request,
#                        _("Only Release Orders in 'Ready To Release' status can be processed for final release."))

        # For HTMX requests, return error response with proper headers
        if request.headers.get('HX-Request'):
            trigger_data = {
                "showNotificationEvent": {
                    "message": str(_("Only Release Orders in 'Ready To Release' status can be processed for final release.")),
                    "type": "error"
                }
            }
            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            return HttpResponse(status=400, headers=headers, content="Release order not eligible for release action.")

        return HttpResponse(status=400, content="Release order not eligible for release action.")

    try:
        # Update the release order status to COMPLETED
        release_order.status = WarehouseReleaseOrder.Status.COMPLETED
        release_order.save(update_fields=['status'])

        # For HTMX requests, return success response
        if request.headers.get('HX-Request'):
            trigger_data = {
                "showNotificationEvent": {
                    "message": "Release order completed successfully!",
                    "type": "success"
                },
                # Add a refresh trigger to reload the current page
                "refreshPage": True
            }

            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            return HttpResponse(status=200, headers=headers)

        # For non-HTMX requests, redirect back to the detail page with success message
        messages.success(request, _("Release order completed successfully."))
        return redirect(reverse("releases:orders:panel", kwargs={"pk": pk}))

    except Exception as e:
        # If an error occurs, return an error response
        if request.headers.get('HX-Request'):
            trigger_data = {
                "showNotificationEvent": {
                    "message": _("Failed to complete release order: %(error)s") % {'error': str(e)},
                    "type": "error"
                }
            }
            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            return HttpResponse(status=500, headers=headers)

        messages.error(request, _("Failed to complete release order: %(error)s") % {'error': str(e)})
        return redirect(reverse("releases:orders:panel", kwargs={"pk": pk}))


def release_order_delete_form(request, pk):
    """
    Display the form for deleting a release order.
    """
    release_order = get_object_or_404(WarehouseReleaseOrder, pk=pk)

    # Check if the release order is in a status that allows deletion
    allowed_statuses = [
        WarehouseReleaseOrder.Status.NEW,
        WarehouseReleaseOrder.Status.PROCESSING,
        WarehouseReleaseOrder.Status.READY_TO_PRINT
    ]

    if release_order.status not in allowed_statuses:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Only Release Orders in 'New', 'Processing', or 'Ready To Print' status can be deleted.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request,
                       _("Only Release Orders in 'New', 'Processing', or 'Ready To Print' status can be deleted."))
        return HttpResponse(status=400, content="Release order not eligible for deletion.")

    context = {
        'release_order': release_order,
        'request': request,
    }

    return render(request, 'releases/partials/release_order_delete_form.html', context)


@require_POST
def release_order_delete(request, pk):
    """
    Delete a release order.
    """
    release_order = get_object_or_404(WarehouseReleaseOrder, pk=pk)

    # Check if the release order is in a status that allows deletion
    allowed_statuses = [
        WarehouseReleaseOrder.Status.NEW,
        WarehouseReleaseOrder.Status.PROCESSING,
        WarehouseReleaseOrder.Status.READY_TO_PRINT
    ]

    if release_order.status not in allowed_statuses:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Only Release Orders in 'New', 'Processing', or 'Ready To Print' status can be deleted.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request,
                       _("Only Release Orders in 'New', 'Processing', or 'Ready To Print' status can be deleted."))
        return HttpResponse(status=400, content="Release order not eligible for deletion.")

    try:
        # Attempt to delete the release order
        if release_order.is_delivery_order:
            release_order.is_delivery_order = False
            release_order.save(update_fields=["is_delivery_order"])

        print(release_order.is_delivery_order)
        release_order.delete()

        # For HTMX requests, return success response
        if request.headers.get('HX-Request'):
            trigger_data = {
                "closeModalEvent": None,
                "showNotificationEvent": {
                    "message": "Release order deleted successfully!",
                    "type": "success"
                },
                # Redirect to the list page after deletion
                "redirectEvent": {"url": reverse("releases:orders:list")}
            }

            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            return HttpResponse(status=200, headers=headers)

        # For non-HTMX requests, redirect to the list page
        messages.success(request, _("Release order deleted successfully."))
        return redirect(reverse("releases:orders:list"))

    except Exception as e:
        # Handle validation errors or other exceptions
        error_message = str(e)

        # For HTMX requests, return error response
        if request.headers.get('HX-Request'):
            trigger_data = {
                "closeModalEvent": None,
                "showNotificationEvent": {
                    "message": f"Failed to delete release order: {error_message}",
                    "type": "error"
                }
            }

            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            return HttpResponse(status=400, headers=headers)

        # For non-HTMX requests, redirect back with error message
        messages.error(request, _("Failed to delete release order: %(error)s") % {'error': error_message})
        return redirect(reverse("releases:orders:panel", kwargs={"pk": pk}))


######################################################################
# Import Bulk Update
######################################################################


class WroImportBulkUpdateExcelView(CoreFormView):
    """Page to import Excel for Rack(s)."""

    # header_title = "Import Bulk Update"
    # selected_page = "releases"
    # selected_subpage = "warehouse_release_orders"

    # permission_required = ("releases.change_warehousereleaseorder",)

    section_title = None
    section_desc = None
    form_class = WroImportBulkEditExcelForm
    template_name = "releases/mains/import_bulk_update.html"
    success_url = reverse_lazy("releases:orders:import_bulk_summary")
    cancel_url = "releases:orders:list"
    submit_text = "Import"
    action_type = "import"
    # success_message = _("Successfully imported bulk update WRO Excel data")

    def post(self, request, *args, **kwargs):
        """
        Handle POST requests: instantiate a form instance with the passed
        POST variables and then check if it's valid.
        """
        form = self.get_form()

        if form.is_valid():
            # To keep track of summary via the form object and pass it to the next view
            request.session["summary"] = form.imported_object.summary

            form_valid = self.form_valid(form)
            return form_valid
        else:
            return self.form_invalid(form)

    def get_context_data(self, **kwargs):
        """
        Add section_title and section_desc to the template context.

        Returns:
            dict: The updated context dictionary
        """
        context = super().get_context_data(**kwargs)
        context['section_title'] = self.section_title
        context['section_desc'] = self.section_desc
        context['submit_text'] = self.submit_text
        context['cancel_url'] = self.cancel_url
        context['action_type'] = self.action_type
        context['view_type'] = 'import'
        context['object_identifier'] = self.get_object_identifier()
        return context


wro_import_bulk_update_excel_view = WroImportBulkUpdateExcelView.as_view()


class WroImportBulkUpdateSummaryView(CoreTemplateView):
    """Page to display Summary of imported Excel for Rack(s) after user clicks Submit button."""

    template_name = "releases/mains/import_bulk_summary.html"

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        context = super().get_context_data(**kwargs)

        # Retrieve data/values from previous view
        context["summary"] = self.request.session["summary"]
        return context


wro_import_bulk_update_summary_view = WroImportBulkUpdateSummaryView.as_view()
