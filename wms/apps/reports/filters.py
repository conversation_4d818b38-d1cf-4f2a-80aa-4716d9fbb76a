from django.db.models import OuterRef, Q, QuerySet, Sum
# from django.utils.translation import gettext_lazy as _
# import django_filters as filters

# from wms.cores.filters import CoreBooleanWidget
from wms.cores.utils import get_user_warehouse_choices, get_item_brand_choices, get_consignor_choices

# from wms.apps.consignors.models import Consignor
from wms.apps.inventories.models import Stock, Transaction
from wms.apps.receives.models import GoodsReceivedNote
from wms.apps.releases.models import WarehouseReleaseOrder

from django.utils.translation import gettext_lazy as _
from django_filters import filters, FilterSet, ChoiceFilter
from django_filters.filters import DateFromToRangeFilter

from wms.cores.forms.widget import(
    CoreSelectWidget, CoreSelectMultipleWidget, CoreTextWidget, CoreDateWidget, CoreDateRangeWidget, CoreNullBooleanSelectWidget
)


def get_grn_status_choices():
    status_choices = [(status, status) for status in GoodsReceivedNote.Status]
    return status_choices


def get_wro_status_choices():
    status_choices = [(status, status) for status in WarehouseReleaseOrder.Status]
    return status_choices


class CustomDateFromToRangeFilter(DateFromToRangeFilter):
    """
    Custom DateFromToRangeFilter that uses date_range_after and date_range_before as parameter names.
    """
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Override the field names to match what we want in the URL
        self.field_name = kwargs.get('field_name')

    def filter(self, qs, value):
        if value:
            lookup_after = f'{self.field_name}__gte'
            lookup_before = f'{self.field_name}__lte'

            filters = {}
            if value.start:
                filters[lookup_after] = value.start
            if value.stop:
                filters[lookup_before] = value.stop

            if filters:
                qs = qs.filter(**filters)
        return qs


# class ReportFilter(filters.FilterSet):
#     keyword_search = filters.CharFilter(
#         label=_("Code or Name contains"),
#         method="custom_keyword_filter",
#     )
#     warehouse = filters.MultipleChoiceFilter(choices=[])
#     date_range = filters.DateFromToRangeFilter(
#         label=_("Date Range"), field_name="date_field", method="filter_by_date_range"
#     )
#     item__brand = filters.MultipleChoiceFilter(method="brand_filter", choices=[])

#     class Meta:
#         model = Stock
#         fields = ["warehouse", "item__consignor", "item__consignor__consignee", "date_range", "item__brand"]

#     def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
#         super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

#         # Override choices within init function. Otherwise, it might trigger
#         # django.db.utils.ProgrammingError when you dropdb, re-createdb, run makemigrations & migrate.
#         self.filters["item__brand"].extra["choices"] = get_item_brand_choices()
#         # Filter out the warehouses based on User Role.
#         self.filters["warehouse"].extra["choices"] = get_user_warehouse_choices(request.user)

#         self.filters["item__consignor"].label = _("Consignor")
#         self.filters["item__consignor__consignee"].label = _("Consignee")
#         self.filters["keyword_search"].field.widget.attrs.update({"class": "form-control"})
#         self.filters["date_range"].field.widget.attrs.update({"class": "form-control"})
#         self.filters["item__consignor__consignee"].field.widget.attrs.update({"class": "form-control core-select2"})
#         self.filters["item__consignor"].field.widget.attrs.update({"class": "form-control core-select2"})
#         self.filters["item__brand"].label = _("Item Brand")

#     @property
#     def qs(self):
#         if not hasattr(self, "_qs"):
#             qs = self.queryset.all()
#             if self.is_bound:
#                 # ensure form validation before filtering
#                 qs = self.filter_queryset(qs)
#             self._qs = qs.annotate(transaction_system_quantity=Sum("transaction__system_quantity"))
#         return self._qs

#     def custom_keyword_filter(self, queryset, name, value):
#         qs = queryset

#         for keyword in value.split():
#             qs = qs.filter(Q(item__code__icontains=keyword) | Q(item__name__icontains=keyword))

#         return qs.distinct()

#     def brand_filter(self, queryset, name, value):
#         qs = queryset

#         qs = qs.filter(item__brand__in=value)

#         return qs.distinct()

#     def filter_by_date_range(self, queryset, name, value):
#         qs = queryset

#         start_date = value.start
#         end_date = value.stop

#         if start_date and end_date:
#             subquery = Transaction.objects.filter(
#                 transaction_datetime__gte=start_date,
#                 transaction_datetime__lte=end_date,
#                 id=OuterRef("transaction__id"),
#             )
#         elif start_date and not end_date:
#             subquery = Transaction.objects.filter(
#                 transaction_datetime__gte=start_date,
#                 id=OuterRef("transaction__id"),
#             )
#         elif end_date and not start_date:
#             subquery = Transaction.objects.filter(
#                 transaction_datetime__lte=end_date,
#                 id=OuterRef("transaction__id"),
#             )

#         qs = qs.filter(transaction__id__in=subquery)

#         return qs


class StockReportFilter(FilterSet):
    """
    Filter class for Item list view.
    Provides filtering capabilities for Item attributes.
    """
    # date_range = filters.DateFromToRangeFilter(
    #     label=_("Arrival Date Range"),
    #     field_name="arrival_datetime",
    #     widget=CoreDateRangeWidget()
    # )
    date_range = filters.DateFromToRangeFilter(
        label=_("Transaction Date Range"),
        field_name="date_field",
        method="filter_by_date_range",
        widget=CoreDateRangeWidget(),
    )
    warehouse = filters.MultipleChoiceFilter(
        label=_("Warehouse"),
        choices=[],
        widget=CoreSelectMultipleWidget()
    )
    item_brand = filters.MultipleChoiceFilter(
        label=_("Item Brand"),
        method="brand_filter",
        choices=[],
        widget=CoreSelectMultipleWidget()
    )

    class Meta:
        model = Stock
        fields = [
            "date_range",
            "warehouse",
            "item__consignor",
            "item__consignor__consignee",
            # "item_brand",
        ]

    def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
        super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

        self.filters["item__consignor"].label = _("Consignor")
        self.filters["item__consignor"].field.widget = CoreSelectWidget()
        self.filters["item__consignor__consignee"].label = _("Consignee")
        self.filters["item__consignor__consignee"].field.widget = CoreSelectMultipleWidget()
        # Dynamically populate location choices from existing data
        if queryset is not None and request is not None:
            # Update field choices after initialization
            self.form.fields['warehouse'].choices = get_user_warehouse_choices(request.user)
            self.form.fields["item_brand"].choices = get_item_brand_choices()

    # @property
    # def qs(self):
    #     if not hasattr(self, "_qs"):
    #         qs = self.queryset.all()
    #         if self.is_bound:
    #             # ensure form validation before filtering
    #             qs = self.filter_queryset(qs)
    #         self._qs = qs.annotate(transaction_system_quantity=Sum("transaction__system_quantity"))
    #     return self._qs

    def filter_by_date_range(self, queryset, name, value):
        qs = queryset

        start_date = value.start
        end_date = value.stop

        if start_date and end_date:
            subquery = Transaction.objects.filter(
                transaction_datetime__gte=start_date,
                transaction_datetime__lte=end_date,
                id=OuterRef("transaction__id"),
            )
        elif start_date and not end_date:
            subquery = Transaction.objects.filter(
                transaction_datetime__gte=start_date,
                id=OuterRef("transaction__id"),
            )
        elif end_date and not start_date:
            subquery = Transaction.objects.filter(
                transaction_datetime__lte=end_date,
                id=OuterRef("transaction__id"),
            )

        qs = qs.filter(transaction__id__in=subquery)

        return qs

    def brand_filter(self, queryset, name, value):
        qs = queryset

        qs = qs.filter(item__brand__in=value)

        return qs.distinct()


class GRNReportFilter(FilterSet):
    """
    Filter class for Item list view.
    Provides filtering capabilities for Item attributes.
    """
    # delivery_to = filters.MultipleChoiceFilter(choices=[])
    date_range = filters.DateFromToRangeFilter(
        label=_("Arrival Date Range"),
        field_name="arrival_datetime",
        widget=CoreDateRangeWidget()
    )
    warehouses = filters.MultipleChoiceFilter(
        label=_("Warehouse"),
        field_name="deliver_to",
        choices=[],
        widget=CoreSelectMultipleWidget()
    )
    consignor = filters.ChoiceFilter(
        choices=[],
        field_name="consignor",
        label=_("Consignor"),
        widget=CoreSelectWidget()
    )
    status = filters.MultipleChoiceFilter(
        choices=get_grn_status_choices(),
        widget=CoreSelectMultipleWidget()
    )

    class Meta:
        model = GoodsReceivedNote
        fields = [
            # "delivery_to",
            "date_range",
            "warehouses",
            "status",
            "consignor",
        ]

    def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
        super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

        # Dynamically populate location choices from existing data
        if queryset is not None and request is not None:
            # Update field choices after initialization
            self.form.fields['warehouses'].choices = get_user_warehouse_choices(request.user)
            self.form.fields['consignor'].choices = get_consignor_choices()

# class GRNReportFilter(filters.FilterSet):
#     keyword_search = filters.CharFilter(
#         label=_("Customer Ref contains"),
#         method="custom_keyword_filter",
#     )
#     delivery_to = filters.MultipleChoiceFilter(choices=[])
#     date_range = filters.DateFromToRangeFilter(
#         label=_("Arrival Date Range"), field_name="date_field", method="filter_by_date_range"
#     )
#     warehouses = filters.MultipleChoiceFilter(label=_("Warehouse"), field_name="deliver_to", choices=[])
#     status = filters.MultipleChoiceFilter(method="status_filter", choices=get_grn_status_choices())

#     class Meta:
#         model = GoodsReceivedNote
#         fields = ["warehouses", "consignor", "date_range", "status"]

#     def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
#         super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

#         # Filter out the warehouses based on User Role.
#         self.filters["warehouses"].extra["choices"] = get_user_warehouse_choices(request.user)

#         self.filters["keyword_search"].field.widget.attrs.update({"class": "form-control"})
#         self.filters["date_range"].field.widget.attrs.update({"class": "form-control"})
#         self.filters["consignor"].field.widget.attrs.update({"class": "form-control core-select2"})
#         self.filters["status"].field.widget.attrs.update({"class": "form-control core-select2"})

#     def custom_keyword_filter(self, queryset, name, value):
#         qs = queryset

#         for keyword in value.split():
#             qs = qs.filter(Q(customer_reference__icontains=keyword))

#         return qs.distinct()

#     def filter_by_date_range(self, queryset, name, value):
#         qs = queryset

#         start_date = value.start
#         end_date = value.stop

#         if start_date and end_date:
#             qs = qs.filter(
#                 arrival_datetime__gte=start_date,
#                 arrival_datetime__lte=end_date,
#             )
#         elif start_date and not end_date:
#             qs = qs.filter(
#                 arrival_datetime__gte=start_date,
#             )
#         elif end_date and not start_date:
#             qs = qs.filter(
#                 arrival_datetime__lte=end_date,
#             )

#         return qs

#     def warehouse_filter(self, queryset, name, value):
#         qs = queryset.filter(deliver_to__in=value)
#         return qs.distinct()

#     def status_filter(self, queryset, name, value):
#         qs = queryset.filter(status__in=value)
#         return qs.distinct()


class WROReportFilter(FilterSet):
    """
    Filter class for Item list view.
    Provides filtering capabilities for Item attributes.
    """
    keyword_search = filters.CharFilter(
        label=_("Customer Ref or DO No. contains"),
        method="custom_keyword_filter",
        widget=CoreTextWidget()
    )
    date_range = filters.DateFromToRangeFilter(
        label=_("Release Date Range"),
        field_name="release_datetime",
        widget=CoreDateRangeWidget()
    )
    warehouses = filters.MultipleChoiceFilter(
        label=_("Warehouse"),
        field_name="warehouses",
        choices=[],
        widget=CoreSelectMultipleWidget()
    )
    consignee__consignor = filters.ChoiceFilter(
        choices=[],
        field_name="consignee__consignor",
        label=_("Consignor"),
        widget=CoreSelectWidget()
    )
    is_delivery_order = filters.BooleanFilter(
        # choices=[],
        field_name="is_delivery_order",
        label=_("Is Delivery Order?"),
        widget=CoreNullBooleanSelectWidget()
    )
    status = filters.MultipleChoiceFilter(
        choices=get_wro_status_choices(),
        widget=CoreSelectMultipleWidget()
    )

    class Meta:
        model = WarehouseReleaseOrder
        fields = [
            "date_range",
            "warehouses",
            "status",
            "keyword_search",
            "consignee__consignor",
            "is_delivery_order",
        ]

    def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
        super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

        # Dynamically populate location choices from existing data
        if queryset is not None and request is not None:
            # Update field choices after initialization
            self.form.fields['warehouses'].choices = get_user_warehouse_choices(request.user)
            self.form.fields['consignee__consignor'].choices = get_consignor_choices()

    def custom_keyword_filter(
        self, queryset: QuerySet[WarehouseReleaseOrder], name: str, value: str
    ) -> QuerySet[WarehouseReleaseOrder]:
        qs = queryset

        for keyword in value.split():
            qs = qs.filter(
                Q(customer_reference__icontains=keyword)
                | Q(deliveryorder__system_number__icontains=keyword)
            )

        return qs.distinct()


# class WROReportFilter(filters.FilterSet):
#     keyword_search = filters.CharFilter(
#         label=_("Customer Ref or DO No. contains"),
#         method="custom_keyword_filter",
#     )
#     date_range = filters.DateFromToRangeFilter(
#         label=_("Release Date Range"), field_name="date_field", method="filter_by_date_range"
#     )
#     status = filters.MultipleChoiceFilter(method="status_filter", choices=get_wro_status_choices())
#     is_delivery_order = filters.BooleanFilter(widget=CoreBooleanWidget)
#     warehouses = filters.MultipleChoiceFilter(choices=[])

#     class Meta:
#         model = WarehouseReleaseOrder
#         fields = ["consignee__consignor", "date_range", "status", "warehouses"]

#     def __init__(self, data=None, queryset=None, *, request=None, prefix=None):
#         super().__init__(data=data, queryset=queryset, request=request, prefix=prefix)

#         # Filter out the warehouses based on User Role.
#         self.filters["warehouses"].extra["choices"] = get_user_warehouse_choices(request.user)

#         self.filters["keyword_search"].field.widget.attrs.update({"class": "form-control"})
#         self.filters["date_range"].field.widget.attrs.update({"class": "form-control"})
#         self.filters["consignee__consignor"].field.label = _("Consignor")
#         self.filters["consignee__consignor"].field.widget.attrs.update({"class": "form-control core-select2"})
#         self.filters["is_delivery_order"].field.widget.attrs.update({"class": "form-control core-select2"})
#         self.filters["status"].field.widget.attrs.update({"class": "form-control core-select2"})
#         self.filters["warehouses"].field.widget.attrs.update({"class": "form-control core-select2"})

#     def custom_keyword_filter(
#         self, queryset: QuerySet[WarehouseReleaseOrder], name: str, value: str
#     ) -> QuerySet[WarehouseReleaseOrder]:
#         qs = queryset

#         for keyword in value.split():
#             qs = qs.filter(Q(customer_reference__icontains=keyword) | Q(deliveryorder__numbering__icontains=keyword))

#         return qs.distinct()

#     def filter_by_date_range(
#         self, queryset: QuerySet[WarehouseReleaseOrder], name: str, value: slice
#     ) -> QuerySet[WarehouseReleaseOrder]:
#         qs = queryset

#         start_date = value.start
#         end_date = value.stop

#         if start_date and end_date:
#             qs = qs.filter(
#                 release_datetime__gte=start_date,
#                 release_datetime__lte=end_date,
#             )
#         elif start_date and not end_date:
#             qs = qs.filter(
#                 release_datetime__gte=start_date,
#             )
#         elif end_date and not start_date:
#             qs = qs.filter(
#                 release_datetime__lte=end_date,
#             )

#         return qs

#     def status_filter(self, queryset, name, value):
#         qs = queryset

#         qs = qs.filter(status__in=value)

#         return qs.distinct()
